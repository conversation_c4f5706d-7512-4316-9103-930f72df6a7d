<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>申请维修</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #1A1A1A; color: white; font-family: sans-serif; }
        .upload-area {
            border: 2px dashed #4A5568;
            transition: all 0.3s ease;
        }
        .upload-area.dragover {
            border-color: #F6E05E;
            background-color: rgba(246, 224, 94, 0.1);
        }
        .image-preview {
            position: relative;
            display: inline-block;
        }
        .remove-image {
            position: absolute;
            top: -8px;
            right: -8px;
            background: #EF4444;
            color: white;
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            font-size: 12px;
        }
    </style>
</head>
<body class="pb-32">

    <!-- iOS Status Bar -->
    <div class="fixed top-0 left-0 right-0 h-11 bg-black/30 backdrop-blur-sm z-50 px-4 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
        <div class="flex items-center gap-2"><i class="fa-solid fa-signal"></i><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
    </div>
    
    <!-- Header -->
    <div class="fixed top-11 left-0 right-0 bg-gray-900/90 backdrop-blur-md z-40 px-4 py-3 border-b border-gray-700">
        <div class="flex items-center gap-3">
            <button onclick="history.back()" class="text-white"><i class="fa-solid fa-arrow-left text-xl"></i></button>
            <h1 class="text-lg font-bold">申请维修</h1>
        </div>
    </div>

    <!-- Main Content -->
    <div class="pt-24 px-4">
        <form id="repairForm">
            <!-- Service Type -->
            <div class="bg-gray-800 rounded-xl p-4 mb-4">
                <h3 class="font-bold mb-3 flex items-center gap-2">
                    <i class="fa-solid fa-tools text-yellow-400"></i>
                    维修类型
                </h3>
                <div class="grid grid-cols-2 gap-3">
                    <label class="repair-type-option bg-gray-700 border-2 border-transparent p-3 rounded-lg cursor-pointer text-center">
                        <input type="radio" name="repairType" value="cue" class="hidden">
                        <i class="fa-solid fa-magic-wand-sparkles text-2xl text-blue-400 mb-2"></i>
                        <p class="font-bold text-sm">球杆维修</p>
                    </label>
                    <label class="repair-type-option bg-gray-700 border-2 border-transparent p-3 rounded-lg cursor-pointer text-center">
                        <input type="radio" name="repairType" value="table" class="hidden">
                        <i class="fa-solid fa-table text-2xl text-green-400 mb-2"></i>
                        <p class="font-bold text-sm">球桌维修</p>
                    </label>
                    <label class="repair-type-option bg-gray-700 border-2 border-transparent p-3 rounded-lg cursor-pointer text-center">
                        <input type="radio" name="repairType" value="accessories" class="hidden">
                        <i class="fa-solid fa-gear text-2xl text-purple-400 mb-2"></i>
                        <p class="font-bold text-sm">配件维修</p>
                    </label>
                    <label class="repair-type-option bg-gray-700 border-2 border-transparent p-3 rounded-lg cursor-pointer text-center">
                        <input type="radio" name="repairType" value="custom" class="hidden">
                        <i class="fa-solid fa-handshake text-2xl text-orange-400 mb-2"></i>
                        <p class="font-bold text-sm">定制服务</p>
                    </label>
                </div>
            </div>

            <!-- Product Info -->
            <div class="bg-gray-800 rounded-xl p-4 mb-4">
                <h3 class="font-bold mb-3 flex items-center gap-2">
                    <i class="fa-solid fa-info-circle text-yellow-400"></i>
                    产品信息
                </h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-gray-400 text-sm mb-2">产品名称 *</label>
                        <input type="text" name="productName" placeholder="请输入产品名称" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg outline-none focus:ring-2 focus:ring-yellow-400" required>
                    </div>
                    <div>
                        <label class="block text-gray-400 text-sm mb-2">品牌型号</label>
                        <input type="text" name="brandModel" placeholder="请输入品牌型号" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg outline-none focus:ring-2 focus:ring-yellow-400">
                    </div>
                    <div>
                        <label class="block text-gray-400 text-sm mb-2">购买时间</label>
                        <input type="date" name="purchaseDate" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg outline-none focus:ring-2 focus:ring-yellow-400">
                    </div>
                </div>
            </div>

            <!-- Problem Description -->
            <div class="bg-gray-800 rounded-xl p-4 mb-4">
                <h3 class="font-bold mb-3 flex items-center gap-2">
                    <i class="fa-solid fa-exclamation-triangle text-yellow-400"></i>
                    问题描述
                </h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-gray-400 text-sm mb-2">故障现象 *</label>
                        <textarea name="problemDescription" placeholder="请详细描述遇到的问题..." class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg outline-none focus:ring-2 focus:ring-yellow-400 h-24 resize-none" required></textarea>
                    </div>
                    <div>
                        <label class="block text-gray-400 text-sm mb-2">紧急程度</label>
                        <div class="flex gap-3">
                            <label class="urgency-option bg-gray-700 border-2 border-transparent px-4 py-2 rounded-lg cursor-pointer text-sm">
                                <input type="radio" name="urgency" value="low" class="hidden">
                                <span class="text-green-400">不急</span>
                            </label>
                            <label class="urgency-option bg-gray-700 border-2 border-transparent px-4 py-2 rounded-lg cursor-pointer text-sm">
                                <input type="radio" name="urgency" value="medium" class="hidden" checked>
                                <span class="text-yellow-400">一般</span>
                            </label>
                            <label class="urgency-option bg-yellow-400 border-2 border-yellow-400 px-4 py-2 rounded-lg cursor-pointer text-sm">
                                <input type="radio" name="urgency" value="high" class="hidden">
                                <span class="text-black font-bold">紧急</span>
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Photo Upload -->
            <div class="bg-gray-800 rounded-xl p-4 mb-4">
                <h3 class="font-bold mb-3 flex items-center gap-2">
                    <i class="fa-solid fa-camera text-yellow-400"></i>
                    问题照片
                </h3>
                <div class="upload-area rounded-lg p-6 text-center mb-4" id="uploadArea">
                    <i class="fa-solid fa-cloud-upload-alt text-4xl text-gray-400 mb-2"></i>
                    <p class="text-gray-400 mb-2">点击或拖拽上传照片</p>
                    <p class="text-xs text-gray-500">支持JPG、PNG格式，最多5张</p>
                    <input type="file" id="photoInput" accept="image/*" multiple class="hidden">
                </div>
                <div class="flex gap-2 flex-wrap" id="imagePreview"></div>
            </div>

            <!-- Contact Info -->
            <div class="bg-gray-800 rounded-xl p-4 mb-4">
                <h3 class="font-bold mb-3 flex items-center gap-2">
                    <i class="fa-solid fa-user text-yellow-400"></i>
                    联系信息
                </h3>
                <div class="space-y-4">
                    <div>
                        <label class="block text-gray-400 text-sm mb-2">联系人 *</label>
                        <input type="text" name="contactName" value="明亮的杰瑞" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg outline-none focus:ring-2 focus:ring-yellow-400" required>
                    </div>
                    <div>
                        <label class="block text-gray-400 text-sm mb-2">手机号 *</label>
                        <input type="tel" name="contactPhone" placeholder="请输入手机号" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg outline-none focus:ring-2 focus:ring-yellow-400" required>
                    </div>
                    <div>
                        <label class="block text-gray-400 text-sm mb-2">详细地址</label>
                        <textarea name="address" placeholder="请输入详细地址，方便上门取件" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg outline-none focus:ring-2 focus:ring-yellow-400 h-20 resize-none"></textarea>
                    </div>
                </div>
            </div>

            <!-- Service Options -->
            <div class="bg-gray-800 rounded-xl p-4 mb-4">
                <h3 class="font-bold mb-3 flex items-center gap-2">
                    <i class="fa-solid fa-cog text-yellow-400"></i>
                    服务选项
                </h3>
                <div class="space-y-3">
                    <label class="flex items-center gap-3 p-3 bg-gray-700 rounded-lg cursor-pointer">
                        <input type="checkbox" name="pickupService" class="text-yellow-400">
                        <i class="fa-solid fa-truck text-blue-400"></i>
                        <span class="flex-1">上门取件服务</span>
                        <span class="text-yellow-400 text-sm">免费</span>
                    </label>
                    <label class="flex items-center gap-3 p-3 bg-gray-700 rounded-lg cursor-pointer">
                        <input type="checkbox" name="expressService" class="text-yellow-400">
                        <i class="fa-solid fa-bolt text-yellow-400"></i>
                        <span class="flex-1">加急服务</span>
                        <span class="text-yellow-400 text-sm">+¥50</span>
                    </label>
                    <label class="flex items-center gap-3 p-3 bg-gray-700 rounded-lg cursor-pointer">
                        <input type="checkbox" name="warrantyExtension" class="text-yellow-400">
                        <i class="fa-solid fa-shield-halved text-green-400"></i>
                        <span class="flex-1">延长保修</span>
                        <span class="text-yellow-400 text-sm">+¥30</span>
                    </label>
                </div>
            </div>

            <!-- Expected Time -->
            <div class="bg-gray-800 rounded-xl p-4 mb-6">
                <h3 class="font-bold mb-3 flex items-center gap-2">
                    <i class="fa-solid fa-clock text-yellow-400"></i>
                    期望时间
                </h3>
                <div class="grid grid-cols-2 gap-3">
                    <div>
                        <label class="block text-gray-400 text-sm mb-2">期望取件时间</label>
                        <input type="datetime-local" name="expectedPickupTime" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg outline-none focus:ring-2 focus:ring-yellow-400">
                    </div>
                    <div>
                        <label class="block text-gray-400 text-sm mb-2">期望完成时间</label>
                        <input type="date" name="expectedCompletionDate" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg outline-none focus:ring-2 focus:ring-yellow-400">
                    </div>
                </div>
            </div>
        </form>
    </div>

    <!-- Bottom Action Bar -->
    <div class="fixed bottom-0 left-0 right-0 bg-gray-900/90 backdrop-blur-md border-t border-gray-700 p-4">
        <div class="flex gap-3">
            <button onclick="saveDraft()" class="flex-1 bg-gray-700 text-white py-3 rounded-lg font-bold">
                保存草稿
            </button>
            <button onclick="submitRepair()" class="flex-1 bg-gradient-to-r from-yellow-400 to-orange-500 text-black py-3 rounded-lg font-bold">
                提交申请
            </button>
        </div>
    </div>

    <script>
        let uploadedImages = [];

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            setupFormInteractions();
            setupImageUpload();
            
            // 根据URL参数预选维修类型
            const urlParams = new URLSearchParams(window.location.search);
            const type = urlParams.get('type');
            if (type) {
                preselectRepairType(type);
            }
        });

        function setupFormInteractions() {
            // 维修类型选择
            document.querySelectorAll('.repair-type-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.repair-type-option').forEach(opt => {
                        opt.className = 'repair-type-option bg-gray-700 border-2 border-transparent p-3 rounded-lg cursor-pointer text-center';
                    });
                    this.className = 'repair-type-option bg-yellow-400 border-2 border-yellow-400 p-3 rounded-lg cursor-pointer text-center';
                    this.querySelector('input').checked = true;
                });
            });

            // 紧急程度选择
            document.querySelectorAll('.urgency-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.urgency-option').forEach(opt => {
                        opt.className = 'urgency-option bg-gray-700 border-2 border-transparent px-4 py-2 rounded-lg cursor-pointer text-sm';
                    });
                    
                    const urgency = this.querySelector('input').value;
                    let className = 'urgency-option border-2 px-4 py-2 rounded-lg cursor-pointer text-sm ';
                    
                    switch(urgency) {
                        case 'low':
                            className += 'bg-green-400 border-green-400';
                            break;
                        case 'medium':
                            className += 'bg-yellow-400 border-yellow-400';
                            break;
                        case 'high':
                            className += 'bg-red-400 border-red-400';
                            break;
                    }
                    
                    this.className = className;
                    this.querySelector('input').checked = true;
                });
            });
        }

        function setupImageUpload() {
            const uploadArea = document.getElementById('uploadArea');
            const photoInput = document.getElementById('photoInput');

            uploadArea.addEventListener('click', () => photoInput.click());
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('drop', handleDrop);
            photoInput.addEventListener('change', handleFileSelect);
        }

        function handleDragOver(e) {
            e.preventDefault();
            e.currentTarget.classList.add('dragover');
        }

        function handleDrop(e) {
            e.preventDefault();
            e.currentTarget.classList.remove('dragover');
            const files = Array.from(e.dataTransfer.files);
            processFiles(files);
        }

        function handleFileSelect(e) {
            const files = Array.from(e.target.files);
            processFiles(files);
        }

        function processFiles(files) {
            const imageFiles = files.filter(file => file.type.startsWith('image/'));
            
            if (uploadedImages.length + imageFiles.length > 5) {
                alert('最多只能上传5张图片');
                return;
            }

            imageFiles.forEach(file => {
                const reader = new FileReader();
                reader.onload = function(e) {
                    uploadedImages.push({
                        file: file,
                        url: e.target.result
                    });
                    updateImagePreview();
                };
                reader.readAsDataURL(file);
            });
        }

        function updateImagePreview() {
            const preview = document.getElementById('imagePreview');
            preview.innerHTML = uploadedImages.map((image, index) => `
                <div class="image-preview">
                    <img src="${image.url}" class="w-20 h-20 object-cover rounded-lg" alt="Preview">
                    <div class="remove-image" onclick="removeImage(${index})">
                        <i class="fa-solid fa-times"></i>
                    </div>
                </div>
            `).join('');
        }

        function removeImage(index) {
            uploadedImages.splice(index, 1);
            updateImagePreview();
        }

        function preselectRepairType(type) {
            const option = document.querySelector(`input[value="${type}"]`);
            if (option) {
                option.closest('.repair-type-option').click();
            }
        }

        function saveDraft() {
            const formData = new FormData(document.getElementById('repairForm'));
            localStorage.setItem('repairDraft', JSON.stringify(Object.fromEntries(formData)));
            alert('草稿已保存');
        }

        function submitRepair() {
            const form = document.getElementById('repairForm');
            
            // 验证必填字段
            if (!form.checkValidity()) {
                form.reportValidity();
                return;
            }

            // 验证维修类型
            if (!document.querySelector('input[name="repairType"]:checked')) {
                alert('请选择维修类型');
                return;
            }

            // 确认提交
            if (confirm('确认提交维修申请？')) {
                // 模拟提交过程
                const submitBtn = document.querySelector('button[onclick="submitRepair()"]');
                submitBtn.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i> 提交中...';
                submitBtn.disabled = true;

                setTimeout(() => {
                    // 跳转到成功页面
                    window.location.href = 'repair_success.html';
                }, 2000);
            }
        }
    </script>

</body>
</html>
