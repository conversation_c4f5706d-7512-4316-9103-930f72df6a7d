<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #1A1A1A; color: white; font-family: sans-serif; }
        .profile-card {
            background: linear-gradient(135deg, #2D3748 0%, #1A202C 100%);
        }
        .glow-yellow { box-shadow: 0 0 15px rgba(255, 215, 0, 0.5); }
    </style>
</head>
<body class="pb-24">

    <!-- iOS Status Bar -->
    <div class="fixed top-0 left-0 right-0 h-11 bg-black/30 backdrop-blur-sm z-50 px-4 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
        <div class="flex items-center gap-2"><i class="fa-solid fa-signal"></i><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
    </div>
    
    <!-- Header -->
    <div class="fixed top-11 left-0 right-0 bg-gray-900/90 backdrop-blur-md z-40 px-4 py-3 border-b border-gray-700">
        <div class="flex items-center justify-between">
            <h1 class="text-lg font-bold">个人中心</h1>
            <div class="flex items-center gap-3">
                <button onclick="openSettings()" class="text-white"><i class="fa-solid fa-cog text-xl"></i></button>
                <button onclick="openNotifications()" class="text-white relative">
                    <i class="fa-solid fa-bell text-xl"></i>
                    <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">3</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="pt-24">
        <!-- User Profile -->
        <div class="profile-card p-6 mx-4 rounded-2xl mb-4 relative overflow-hidden">
            <div class="absolute top-0 right-0 w-32 h-32 bg-yellow-400/10 rounded-full -translate-y-16 translate-x-16"></div>
            <div class="relative z-10">
                <div class="flex items-center gap-4 mb-4">
                    <div class="relative">
                        <img src="https://i.pravatar.cc/80?u=user" class="w-20 h-20 rounded-full border-3 border-yellow-400" alt="User Avatar">
                        <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-gray-800"></div>
                    </div>
                    <div class="flex-1">
                        <h2 class="text-xl font-bold">明亮的杰瑞</h2>
                        <p class="text-gray-400">台球爱好者 · 已加入365天</p>
                        <div class="flex items-center gap-2 mt-2">
                            <span class="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-3 py-1 rounded-full text-xs font-bold">VIP黄金会员</span>
                            <span class="text-yellow-400 text-sm font-bold">积分: 2580</span>
                        </div>
                    </div>
                    <button onclick="editProfile()" class="text-gray-400 hover:text-yellow-400 transition-colors">
                        <i class="fa-solid fa-edit text-xl"></i>
                    </button>
                </div>
                
                <!-- Member Progress -->
                <div class="bg-gray-700/50 rounded-xl p-3 mt-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-sm text-gray-400">会员等级进度</span>
                        <span class="text-sm text-yellow-400">距离钻石会员还需1420积分</span>
                    </div>
                    <div class="w-full bg-gray-600 rounded-full h-2">
                        <div class="bg-gradient-to-r from-yellow-400 to-orange-500 h-2 rounded-full" style="width: 65%"></div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-4 gap-3 px-4 mb-6">
            <div class="bg-gray-800 rounded-xl p-4 text-center">
                <i class="fa-solid fa-calendar-check text-yellow-400 text-xl mb-2"></i>
                <p class="text-lg font-bold text-white">12</p>
                <p class="text-xs text-gray-400">本月课程</p>
            </div>
            <div class="bg-gray-800 rounded-xl p-4 text-center">
                <i class="fa-solid fa-shopping-bag text-green-400 text-xl mb-2"></i>
                <p class="text-lg font-bold text-white">8</p>
                <p class="text-xs text-gray-400">已购商品</p>
            </div>
            <div class="bg-gray-800 rounded-xl p-4 text-center">
                <i class="fa-solid fa-star text-blue-400 text-xl mb-2"></i>
                <p class="text-lg font-bold text-white">4.8</p>
                <p class="text-xs text-gray-400">平均评分</p>
            </div>
            <div class="bg-gray-800 rounded-xl p-4 text-center">
                <i class="fa-solid fa-coins text-purple-400 text-xl mb-2"></i>
                <p class="text-lg font-bold text-white">¥156</p>
                <p class="text-xs text-gray-400">余额</p>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="px-4 mb-6">
            <div class="grid grid-cols-4 gap-3">
                <button onclick="goToOrders()" class="bg-gray-800 rounded-xl p-4 text-center hover:bg-gray-700 transition-colors">
                    <i class="fa-solid fa-file-invoice text-2xl text-yellow-400 mb-2"></i>
                    <p class="text-xs font-bold">订单管理</p>
                </button>
                <button onclick="goToMembership()" class="bg-gray-800 rounded-xl p-4 text-center hover:bg-gray-700 transition-colors">
                    <i class="fa-solid fa-crown text-2xl text-orange-400 mb-2"></i>
                    <p class="text-xs font-bold">会员中心</p>
                </button>
                <button onclick="goToCoupons()" class="bg-gray-800 rounded-xl p-4 text-center hover:bg-gray-700 transition-colors">
                    <i class="fa-solid fa-ticket text-2xl text-green-400 mb-2"></i>
                    <p class="text-xs font-bold">优惠券</p>
                </button>
                <button onclick="goToDistribution()" class="bg-gray-800 rounded-xl p-4 text-center hover:bg-gray-700 transition-colors">
                    <i class="fa-solid fa-users text-2xl text-blue-400 mb-2"></i>
                    <p class="text-xs font-bold">分销中心</p>
                </button>
            </div>
        </div>

        <!-- Menu Items -->
        <div class="px-4 space-y-2">
            <!-- 订单相关 -->
            <div class="bg-gray-800 rounded-xl p-4">
                <h3 class="font-bold mb-3 text-gray-300">订单服务</h3>
                <div class="grid grid-cols-2 gap-3">
                    <a href="my_bookings.html" class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fa-solid fa-calendar-check text-yellow-400 text-lg w-5"></i>
                        <span class="text-sm font-semibold">我的预约</span>
                    </a>
                    <a href="cart.html" class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fa-solid fa-shopping-cart text-green-400 text-lg w-5"></i>
                        <span class="text-sm font-semibold">购物订单</span>
                    </a>
                    <a href="repair_progress.html" class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fa-solid fa-screwdriver-wrench text-blue-400 text-lg w-5"></i>
                        <span class="text-sm font-semibold">维修订单</span>
                    </a>
                    <a href="recycle_service.html" class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fa-solid fa-recycle text-purple-400 text-lg w-5"></i>
                        <span class="text-sm font-semibold">回收订单</span>
                    </a>
                </div>
            </div>

            <!-- 会员权益 -->
            <div class="bg-gray-800 rounded-xl p-4">
                <h3 class="font-bold mb-3 text-gray-300">会员权益</h3>
                <div class="space-y-2">
                    <a href="membership_center.html" class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fa-solid fa-crown text-orange-400 text-lg w-5"></i>
                        <span class="flex-1 text-sm font-semibold">会员中心</span>
                        <span class="text-xs bg-orange-400 text-black px-2 py-1 rounded-full">VIP</span>
                    </a>
                    <a href="coupon_center.html" class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fa-solid fa-ticket text-green-400 text-lg w-5"></i>
                        <span class="flex-1 text-sm font-semibold">优惠券中心</span>
                        <span class="text-xs bg-red-500 text-white px-2 py-1 rounded-full">3张</span>
                    </a>
                    <a href="points_mall.html" class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fa-solid fa-coins text-yellow-400 text-lg w-5"></i>
                        <span class="flex-1 text-sm font-semibold">积分商城</span>
                        <span class="text-xs text-yellow-400">2580积分</span>
                    </a>
                    <a href="balance_center.html" class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fa-solid fa-wallet text-blue-400 text-lg w-5"></i>
                        <span class="flex-1 text-sm font-semibold">储值中心</span>
                        <span class="text-xs text-blue-400">¥156</span>
                    </a>
                </div>
            </div>

            <!-- 社交分享 -->
            <div class="bg-gray-800 rounded-xl p-4">
                <h3 class="font-bold mb-3 text-gray-300">社交分享</h3>
                <div class="space-y-2">
                    <a href="distribution_center.html" class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fa-solid fa-users text-purple-400 text-lg w-5"></i>
                        <span class="flex-1 text-sm font-semibold">分销中心</span>
                        <span class="text-xs bg-purple-400 text-white px-2 py-1 rounded-full">赚佣金</span>
                    </a>
                    <a href="#" class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fa-solid fa-heart text-red-400 text-lg w-5"></i>
                        <span class="text-sm font-semibold">我的收藏</span>
                    </a>
                    <a href="#" class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fa-solid fa-share text-green-400 text-lg w-5"></i>
                        <span class="text-sm font-semibold">邀请好友</span>
                    </a>
                </div>
            </div>

            <!-- 设置帮助 -->
            <div class="bg-gray-800 rounded-xl p-4">
                <h3 class="font-bold mb-3 text-gray-300">设置帮助</h3>
                <div class="space-y-2">
                    <a href="#" class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fa-solid fa-cog text-gray-400 text-lg w-5"></i>
                        <span class="text-sm font-semibold">账户设置</span>
                    </a>
                    <a href="#" class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fa-solid fa-headset text-blue-400 text-lg w-5"></i>
                        <span class="text-sm font-semibold">客服中心</span>
                    </a>
                    <a href="#" class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fa-solid fa-question-circle text-yellow-400 text-lg w-5"></i>
                        <span class="text-sm font-semibold">帮助中心</span>
                    </a>
                    <a href="#" class="flex items-center gap-3 p-2 rounded-lg hover:bg-gray-700 transition-colors">
                        <i class="fa-solid fa-info-circle text-green-400 text-lg w-5"></i>
                        <span class="text-sm font-semibold">关于我们</span>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 h-20 bg-gray-900/80 backdrop-blur-md flex justify-around items-center text-gray-400 border-t border-gray-700">
        <a href="home_rich.html" class="flex flex-col items-center gap-1"><i class="fa-solid fa-house text-xl"></i><span class="text-xs">首页</span></a>
        <a href="mall_list_enhanced.html" class="flex flex-col items-center gap-1"><i class="fa-solid fa-store text-xl"></i><span class="text-xs">商城</span></a>
        <a href="#" class="w-16 h-16 -mt-8 bg-yellow-400 rounded-full flex items-center justify-center text-black shadow-lg"><i class="fa-solid fa-qrcode text-3xl"></i></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-file-invoice text-xl"></i><span class="text-xs">订单</span></a>
        <a href="#" class="flex flex-col items-center gap-1 text-yellow-400"><i class="fa-solid fa-user text-xl"></i><span class="text-xs font-bold">我的</span></a>
    </div>

    <script>
        function editProfile() {
            window.location.href = 'profile_edit.html';
        }

        function openSettings() {
            window.location.href = 'settings.html';
        }

        function openNotifications() {
            window.location.href = 'notifications.html';
        }

        function goToOrders() {
            window.location.href = 'order_list.html';
        }

        function goToMembership() {
            window.location.href = 'membership_center.html';
        }

        function goToCoupons() {
            window.location.href = 'coupon_center.html';
        }

        function goToDistribution() {
            window.location.href = 'distribution_center.html';
        }
    </script>

</body>
</html>
