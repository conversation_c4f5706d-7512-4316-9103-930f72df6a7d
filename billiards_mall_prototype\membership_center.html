<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>会员中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #1A1A1A; color: white; font-family: sans-serif; }
        .membership-card {
            background: linear-gradient(135deg, #F59E0B 0%, #D97706 100%);
        }
        .privilege-card {
            background: linear-gradient(135deg, #2D3748 0%, #1A202C 100%);
            border: 1px solid #4A5568;
        }
        .glow-gold { box-shadow: 0 0 20px rgba(245, 158, 11, 0.5); }
    </style>
</head>
<body class="pb-24">

    <!-- iOS Status Bar -->
    <div class="fixed top-0 left-0 right-0 h-11 bg-black/30 backdrop-blur-sm z-50 px-4 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
        <div class="flex items-center gap-2"><i class="fa-solid fa-signal"></i><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
    </div>
    
    <!-- Header -->
    <div class="fixed top-11 left-0 right-0 bg-gray-900/90 backdrop-blur-md z-40 px-4 py-3 border-b border-gray-700">
        <div class="flex items-center gap-3">
            <button onclick="history.back()" class="text-white"><i class="fa-solid fa-arrow-left text-xl"></i></button>
            <h1 class="text-lg font-bold flex-1">会员中心</h1>
            <button onclick="viewHistory()" class="text-yellow-400">历史记录</button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="pt-24">
        <!-- Current Membership Card -->
        <div class="membership-card mx-4 rounded-2xl p-6 mb-6 relative overflow-hidden glow-gold">
            <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
            <div class="relative z-10">
                <div class="flex items-center justify-between mb-4">
                    <div class="flex items-center gap-3">
                        <i class="fa-solid fa-crown text-3xl text-white"></i>
                        <div>
                            <h2 class="text-xl font-bold text-white">VIP黄金会员</h2>
                            <p class="text-orange-100 text-sm">尊享特权，品质生活</p>
                        </div>
                    </div>
                    <div class="text-right">
                        <p class="text-white text-sm">有效期至</p>
                        <p class="text-white font-bold">2024.12.31</p>
                    </div>
                </div>
                
                <div class="bg-white/20 rounded-xl p-4 mb-4">
                    <div class="flex items-center justify-between mb-2">
                        <span class="text-white text-sm">升级进度</span>
                        <span class="text-white text-sm">距离钻石会员还需1420积分</span>
                    </div>
                    <div class="w-full bg-white/30 rounded-full h-3">
                        <div class="bg-white h-3 rounded-full" style="width: 65%"></div>
                    </div>
                    <div class="flex justify-between mt-2 text-xs text-white/80">
                        <span>当前积分: 2580</span>
                        <span>目标积分: 4000</span>
                    </div>
                </div>

                <div class="grid grid-cols-3 gap-4 text-center">
                    <div>
                        <p class="text-2xl font-bold text-white">8.5折</p>
                        <p class="text-xs text-orange-100">商城折扣</p>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-white">免费</p>
                        <p class="text-xs text-orange-100">配送服务</p>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-white">专属</p>
                        <p class="text-xs text-orange-100">客服通道</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Membership Levels -->
        <div class="px-4 mb-6">
            <h3 class="font-bold text-lg mb-4">会员等级</h3>
            <div class="space-y-3">
                <!-- Bronze -->
                <div class="privilege-card rounded-xl p-4 opacity-60">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-12 bg-amber-600 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-medal text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-bold">青铜会员</h4>
                            <p class="text-sm text-gray-400">0-999积分 · 9.5折优惠</p>
                        </div>
                        <span class="text-xs bg-gray-600 text-gray-300 px-2 py-1 rounded">已达成</span>
                    </div>
                </div>

                <!-- Silver -->
                <div class="privilege-card rounded-xl p-4 opacity-60">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-12 bg-gray-400 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-award text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-bold">白银会员</h4>
                            <p class="text-sm text-gray-400">1000-1999积分 · 9折优惠</p>
                        </div>
                        <span class="text-xs bg-gray-600 text-gray-300 px-2 py-1 rounded">已达成</span>
                    </div>
                </div>

                <!-- Gold (Current) -->
                <div class="privilege-card rounded-xl p-4 border-2 border-yellow-400">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-12 bg-yellow-500 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-crown text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-bold text-yellow-400">黄金会员</h4>
                            <p class="text-sm text-gray-400">2000-3999积分 · 8.5折优惠</p>
                        </div>
                        <span class="text-xs bg-yellow-400 text-black px-2 py-1 rounded font-bold">当前等级</span>
                    </div>
                </div>

                <!-- Diamond -->
                <div class="privilege-card rounded-xl p-4">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-gem text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-bold">钻石会员</h4>
                            <p class="text-sm text-gray-400">4000-7999积分 · 8折优惠</p>
                        </div>
                        <span class="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded">未达成</span>
                    </div>
                </div>

                <!-- Platinum -->
                <div class="privilege-card rounded-xl p-4">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-12 bg-purple-500 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-star text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-bold">铂金会员</h4>
                            <p class="text-sm text-gray-400">8000+积分 · 7.5折优惠</p>
                        </div>
                        <span class="text-xs bg-gray-700 text-gray-300 px-2 py-1 rounded">未达成</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Privileges -->
        <div class="px-4 mb-6">
            <h3 class="font-bold text-lg mb-4">当前特权</h3>
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-gray-800 rounded-xl p-4 text-center">
                    <i class="fa-solid fa-percentage text-2xl text-yellow-400 mb-2"></i>
                    <p class="font-bold text-sm">8.5折优惠</p>
                    <p class="text-xs text-gray-400">全场商品</p>
                </div>
                <div class="bg-gray-800 rounded-xl p-4 text-center">
                    <i class="fa-solid fa-shipping-fast text-2xl text-green-400 mb-2"></i>
                    <p class="font-bold text-sm">免费配送</p>
                    <p class="text-xs text-gray-400">无门槛包邮</p>
                </div>
                <div class="bg-gray-800 rounded-xl p-4 text-center">
                    <i class="fa-solid fa-headset text-2xl text-blue-400 mb-2"></i>
                    <p class="font-bold text-sm">专属客服</p>
                    <p class="text-xs text-gray-400">优先响应</p>
                </div>
                <div class="bg-gray-800 rounded-xl p-4 text-center">
                    <i class="fa-solid fa-gift text-2xl text-purple-400 mb-2"></i>
                    <p class="font-bold text-sm">生日礼品</p>
                    <p class="text-xs text-gray-400">专属福利</p>
                </div>
                <div class="bg-gray-800 rounded-xl p-4 text-center">
                    <i class="fa-solid fa-calendar-check text-2xl text-orange-400 mb-2"></i>
                    <p class="font-bold text-sm">优先预约</p>
                    <p class="text-xs text-gray-400">教练课程</p>
                </div>
                <div class="bg-gray-800 rounded-xl p-4 text-center">
                    <i class="fa-solid fa-coins text-2xl text-yellow-500 mb-2"></i>
                    <p class="font-bold text-sm">积分翻倍</p>
                    <p class="text-xs text-gray-400">消费返积分</p>
                </div>
            </div>
        </div>

        <!-- Points Activities -->
        <div class="px-4 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-bold text-lg">积分活动</h3>
                <button onclick="viewAllActivities()" class="text-yellow-400 text-sm">查看全部</button>
            </div>
            <div class="space-y-3">
                <div class="bg-gray-800 rounded-xl p-4">
                    <div class="flex items-center gap-3">
                        <div class="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-calendar-days text-white"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-bold">每日签到</h4>
                            <p class="text-sm text-gray-400">连续签到7天可获得50积分</p>
                        </div>
                        <button onclick="dailyCheckIn()" class="bg-green-500 text-white px-4 py-2 rounded-lg text-sm font-bold">
                            签到
                        </button>
                    </div>
                </div>

                <div class="bg-gray-800 rounded-xl p-4">
                    <div class="flex items-center gap-3">
                        <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-share text-white"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-bold">分享好友</h4>
                            <p class="text-sm text-gray-400">邀请好友注册可获得100积分</p>
                        </div>
                        <button onclick="shareToFriend()" class="bg-blue-500 text-white px-4 py-2 rounded-lg text-sm font-bold">
                            分享
                        </button>
                    </div>
                </div>

                <div class="bg-gray-800 rounded-xl p-4">
                    <div class="flex items-center gap-3">
                        <div class="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-shopping-cart text-white"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-bold">消费返积分</h4>
                            <p class="text-sm text-gray-400">每消费1元可获得2积分</p>
                        </div>
                        <span class="text-yellow-400 text-sm font-bold">进行中</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="px-4 mb-6">
            <div class="grid grid-cols-2 gap-4">
                <button onclick="goToPointsMall()" class="bg-gradient-to-r from-yellow-400 to-orange-500 text-black py-4 rounded-xl font-bold">
                    <i class="fa-solid fa-store mr-2"></i>积分商城
                </button>
                <button onclick="viewPointsHistory()" class="bg-gray-700 text-white py-4 rounded-xl font-bold">
                    <i class="fa-solid fa-history mr-2"></i>积分明细
                </button>
            </div>
        </div>
    </div>

    <!-- Bottom Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 h-20 bg-gray-900/80 backdrop-blur-md flex justify-around items-center text-gray-400 border-t border-gray-700">
        <a href="home_rich.html" class="flex flex-col items-center gap-1"><i class="fa-solid fa-house text-xl"></i><span class="text-xs">首页</span></a>
        <a href="mall_list_enhanced.html" class="flex flex-col items-center gap-1"><i class="fa-solid fa-store text-xl"></i><span class="text-xs">商城</span></a>
        <a href="#" class="w-16 h-16 -mt-8 bg-yellow-400 rounded-full flex items-center justify-center text-black shadow-lg"><i class="fa-solid fa-qrcode text-3xl"></i></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-file-invoice text-xl"></i><span class="text-xs">订单</span></a>
        <a href="profile_enhanced.html" class="flex flex-col items-center gap-1 text-yellow-400"><i class="fa-solid fa-user text-xl"></i><span class="text-xs font-bold">我的</span></a>
    </div>

    <script>
        function viewHistory() {
            window.location.href = 'membership_history.html';
        }

        function viewAllActivities() {
            window.location.href = 'points_activities.html';
        }

        function dailyCheckIn() {
            // 模拟签到
            const button = event.target;
            button.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i>';
            button.disabled = true;

            setTimeout(() => {
                button.innerHTML = '已签到';
                button.className = 'bg-gray-500 text-white px-4 py-2 rounded-lg text-sm font-bold cursor-not-allowed';
                showNotification('签到成功！获得10积分');
            }, 1000);
        }

        function shareToFriend() {
            if (navigator.share) {
                navigator.share({
                    title: '小铁台球会员中心',
                    text: '加入小铁台球，享受专业台球服务！',
                    url: window.location.href
                });
            } else {
                // 降级处理
                const shareText = '加入小铁台球，享受专业台球服务！';
                navigator.clipboard.writeText(shareText).then(() => {
                    showNotification('分享链接已复制到剪贴板');
                });
            }
        }

        function goToPointsMall() {
            window.location.href = 'points_mall.html';
        }

        function viewPointsHistory() {
            window.location.href = 'points_history.html';
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-4 py-2 rounded-lg z-50';
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    </script>

</body>
</html>
