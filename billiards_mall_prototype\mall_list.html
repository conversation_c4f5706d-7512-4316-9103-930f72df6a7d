<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品列表</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style> body { background-color: #1A1A1A; color: white; font-family: sans-serif; } </style>
</head>
<body class="pb-20">

    <!-- Header with Search -->
    <div class="fixed top-0 left-0 right-0 bg-gray-900/80 backdrop-blur-md z-50">
        <div class="h-11 px-4 flex justify-between items-center text-sm font-semibold">... Status Bar ...</div>
        <div class="h-14 px-4 flex items-center gap-3">
            <div class="flex-1 bg-gray-700 rounded-full flex items-center px-4 gap-2">
                <i class="fa-solid fa-search text-gray-400"></i>
                <input type="text" placeholder="搜索球杆、配件" class="w-full bg-transparent text-white placeholder-gray-400 outline-none">
            </div>
            <a href="#" class="text-gray-300"><i class="fa-solid fa-shopping-cart text-2xl"></i></a>
        </div>
    </div>
    
    <div class="pt-28 flex">
        <!-- Sidebar Categories -->
        <nav class="w-24 bg-gray-800 h-screen-minus-header pt-4">
            <ul>
                <li class="p-4 text-center text-yellow-400 bg-gray-900 border-l-4 border-yellow-400 font-bold">全部球杆</li>
                <li class="p-4 text-center text-gray-300">精选配件</li>
                <li class="p-4 text-center text-gray-300">大师球桌</li>
                <li class="p-4 text-center text-gray-300">潮流周边</li>
            </ul>
        </nav>

        <!-- Main Product List -->
        <main class="flex-1 px-4">
            <!-- Filter Bar -->
            <div class="flex justify-between items-center text-sm text-gray-300 py-3">
                <a href="#" class="font-bold text-white">综合</a>
                <a href="#">销量</a>
                <a href="#" class="flex items-center gap-1">价格 <i class="fa-solid fa-sort"></i></a>
                <a href="#" class="flex items-center gap-1">筛选 <i class="fa-solid fa-filter"></i></a>
            </div>

            <!-- Product Grid -->
            <div class="grid grid-cols-2 gap-3">
                <!-- Repeat Product Card... -->
                <div class="bg-gray-800 rounded-xl overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1557996739-b883c79d1297?q=80&w=600&auto=format&fit=crop" class="w-full h-32 object-cover" alt="Product">
                    <div class="p-2">
                        <p class="text-sm font-semibold h-8 overflow-hidden">Cuppa-JR 职业级枫木球杆</p>
                        <p class="text-base font-bold text-yellow-400 mt-1">¥ 1280</p>
                    </div>
                </div>
                 <div class="bg-gray-800 rounded-xl overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1631181827493-276949033331?q=80&w=600&auto=format&fit=crop" class="w-full h-32 object-cover" alt="Product">
                    <div class="p-2">
                        <p class="text-sm font-semibold h-8 overflow-hidden">进口比利时水晶球 标准版全套</p>
                        <p class="text-base font-bold text-yellow-400 mt-1">¥ 899</p>
                    </div>
                </div>
                <!-- ... more cards -->
            </div>
        </main>
    </div>

</body>
</html>