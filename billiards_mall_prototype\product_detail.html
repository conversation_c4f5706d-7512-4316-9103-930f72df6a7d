<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #1A1A1A; color: white; font-family: sans-serif; }
        .glow-yellow { box-shadow: 0 0 15px rgba(255, 215, 0, 0.5); }
        .spec-modal {
            transform: translateY(100%);
            transition: transform 0.3s ease-in-out;
        }
        .spec-modal.active {
            transform: translateY(0);
        }
        .share-modal {
            opacity: 0;
            transform: scale(0.8);
            transition: all 0.3s ease-in-out;
        }
        .share-modal.active {
            opacity: 1;
            transform: scale(1);
        }
    </style>
</head>
<body class="pb-24">

    <!-- iOS Status Bar -->
    <div class="fixed top-0 left-0 right-0 h-11 bg-black/30 backdrop-blur-sm z-50 px-4 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
        <div class="flex items-center gap-2"><i class="fa-solid fa-signal"></i><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
    </div>
    
    <!-- Header -->
    <div class="fixed top-11 left-0 right-0 bg-gray-900/90 backdrop-blur-md z-40 px-4 py-3 border-b border-gray-700">
        <div class="flex items-center justify-between">
            <button onclick="history.back()" class="text-white"><i class="fa-solid fa-arrow-left text-xl"></i></button>
            <h1 class="text-lg font-bold">商品详情</h1>
            <div class="flex items-center gap-3">
                <button onclick="openShare()" class="text-white"><i class="fa-solid fa-share text-xl"></i></button>
                <button onclick="openCart()" class="text-white relative">
                    <i class="fa-solid fa-shopping-cart text-xl"></i>
                    <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center" id="cartCount">2</span>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div class="pt-24">
        <!-- Product Images Carousel -->
        <div class="relative h-80 bg-gray-800">
            <div class="flex transition-transform duration-500 ease-in-out h-full" id="imageCarousel">
                <div class="w-full flex-shrink-0 relative">
                    <img src="https://images.unsplash.com/photo-1557996739-b883c79d1297?q=80&w=800&auto=format&fit=crop" class="w-full h-full object-cover" alt="Product Image 1">
                </div>
                <div class="w-full flex-shrink-0 relative">
                    <img src="https://images.unsplash.com/photo-1574116035011-e68651815f43?q=80&w=800&auto=format&fit=crop" class="w-full h-full object-cover" alt="Product Image 2">
                </div>
                <div class="w-full flex-shrink-0 relative">
                    <img src="https://images.unsplash.com/photo-1559523161-0d5b3d99d32b?q=80&w=800&auto=format&fit=crop" class="w-full h-full object-cover" alt="Product Image 3">
                </div>
            </div>
            <!-- Image Indicators -->
            <div class="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
                <div class="w-2 h-2 rounded-full bg-white" id="img-indicator-0"></div>
                <div class="w-2 h-2 rounded-full bg-white/50" id="img-indicator-1"></div>
                <div class="w-2 h-2 rounded-full bg-white/50" id="img-indicator-2"></div>
            </div>
            <!-- Preview Button -->
            <button onclick="previewImages()" class="absolute top-4 right-4 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                <i class="fa-solid fa-expand"></i> 预览
            </button>
        </div>

        <!-- Product Info -->
        <div class="p-4 bg-gray-800">
            <div class="flex items-start justify-between">
                <div class="flex-1">
                    <p class="text-3xl font-bold text-yellow-400">¥ <span id="currentPrice">1280</span></p>
                    <p class="text-sm text-gray-400 line-through mt-1">原价 ¥1580</p>
                </div>
                <button onclick="openShare()" class="bg-gray-700 p-3 rounded-full">
                    <i class="fa-solid fa-share text-yellow-400"></i>
                </button>
            </div>
            <h1 class="text-xl font-bold mt-4" id="productTitle">Cuppa-JR 职业级枫木球杆</h1>
            <p class="text-sm text-gray-400 mt-2">专业级台球杆，采用优质枫木制作，手感舒适，精准度高</p>
            
            <!-- Rating and Sales -->
            <div class="flex items-center gap-4 mt-4">
                <div class="flex items-center gap-1">
                    <div class="flex text-yellow-400 text-sm">
                        <i class="fa-solid fa-star"></i>
                        <i class="fa-solid fa-star"></i>
                        <i class="fa-solid fa-star"></i>
                        <i class="fa-solid fa-star"></i>
                        <i class="fa-solid fa-star"></i>
                    </div>
                    <span class="text-sm text-gray-400">5.0</span>
                </div>
                <span class="text-sm text-gray-400">已售128件</span>
                <span class="text-sm text-gray-400">库存99件</span>
            </div>
        </div>

        <!-- Spec Selection -->
        <div class="p-4 bg-gray-800 mt-2">
            <div class="flex items-center justify-between" onclick="openSpecModal()">
                <div>
                    <p class="font-bold">规格选择</p>
                    <p class="text-sm text-gray-400 mt-1" id="selectedSpec">请选择 颜色 重量</p>
                </div>
                <i class="fa-solid fa-chevron-right text-gray-400"></i>
            </div>
        </div>

        <!-- Service Info -->
        <div class="p-4 bg-gray-800 mt-2">
            <h3 class="font-bold mb-3">服务保障</h3>
            <div class="space-y-2">
                <div class="flex items-center gap-2">
                    <i class="fa-solid fa-shield-halved text-green-400"></i>
                    <span class="text-sm">正品保证</span>
                </div>
                <div class="flex items-center gap-2">
                    <i class="fa-solid fa-truck text-blue-400"></i>
                    <span class="text-sm">全国包邮</span>
                </div>
                <div class="flex items-center gap-2">
                    <i class="fa-solid fa-rotate-left text-yellow-400"></i>
                    <span class="text-sm">7天无理由退换</span>
                </div>
            </div>
        </div>

        <!-- Product Details -->
        <div class="p-4 bg-gray-800 mt-2">
            <h3 class="font-bold mb-3">商品详情</h3>
            <div class="space-y-4">
                <img src="https://images.unsplash.com/photo-1557996739-b883c79d1297?q=80&w=800&auto=format&fit=crop" class="w-full rounded-lg" alt="Detail Image">
                <p class="text-sm text-gray-300 leading-relaxed">
                    这款Cuppa-JR职业级枫木球杆采用精选北美枫木制作，经过严格的工艺处理，确保每一支球杆都具有出色的性能。
                    球杆重量适中，平衡性佳，适合各种技术水平的球手使用。
                </p>
                <img src="https://images.unsplash.com/photo-1574116035011-e68651815f43?q=80&w=800&auto=format&fit=crop" class="w-full rounded-lg" alt="Detail Image">
                <p class="text-sm text-gray-300 leading-relaxed">
                    精工细作的杆头设计，配合专业级皮头，提供优异的击球感受和精准的控制力。
                    无论是练习还是比赛，都能满足您的需求。
                </p>
            </div>
        </div>
    </div>

    <!-- Bottom Action Bar -->
    <div class="fixed bottom-0 left-0 right-0 bg-gray-900/90 backdrop-blur-md border-t border-gray-700 p-4">
        <div class="flex items-center gap-3">
            <button onclick="contactService()" class="flex flex-col items-center gap-1 text-gray-400">
                <i class="fa-solid fa-headset text-xl"></i>
                <span class="text-xs">客服</span>
            </button>
            <button onclick="openCart()" class="flex flex-col items-center gap-1 text-gray-400 relative">
                <i class="fa-solid fa-shopping-cart text-xl"></i>
                <span class="text-xs">购物车</span>
                <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-4 h-4 flex items-center justify-center">2</span>
            </button>
            <button onclick="addToCart()" class="flex-1 bg-gray-700 text-white py-3 rounded-lg font-bold ml-3">
                加入购物车
            </button>
            <button onclick="buyNow()" class="flex-1 bg-gradient-to-r from-yellow-400 to-orange-500 text-black py-3 rounded-lg font-bold glow-yellow">
                立即购买
            </button>
        </div>
    </div>

    <!-- Spec Selection Modal -->
    <div class="fixed inset-0 bg-black/50 z-50 hidden" id="specOverlay" onclick="closeSpecModal()">
        <div class="spec-modal fixed bottom-0 left-0 right-0 bg-gray-900 rounded-t-2xl p-6" onclick="event.stopPropagation()">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-bold">选择规格</h3>
                <button onclick="closeSpecModal()" class="text-gray-400">
                    <i class="fa-solid fa-times text-xl"></i>
                </button>
            </div>
            
            <!-- Product Summary -->
            <div class="flex gap-4 mb-6">
                <img src="https://images.unsplash.com/photo-1557996739-b883c79d1297?q=80&w=100&auto=format&fit=crop" class="w-20 h-20 rounded-lg object-cover" alt="Product">
                <div class="flex-1">
                    <p class="font-bold">Cuppa-JR 职业级枫木球杆</p>
                    <p class="text-2xl font-bold text-yellow-400 mt-1">¥ <span id="modalPrice">1280</span></p>
                    <p class="text-sm text-gray-400">库存99件</p>
                </div>
            </div>
            
            <!-- Color Selection -->
            <div class="mb-6">
                <h4 class="font-bold mb-3">颜色</h4>
                <div class="flex gap-3">
                    <button class="spec-option border-2 border-yellow-400 bg-yellow-400/20 px-4 py-2 rounded-lg text-sm" data-type="color" data-value="原木色">原木色</button>
                    <button class="spec-option border-2 border-gray-600 px-4 py-2 rounded-lg text-sm" data-type="color" data-value="深棕色">深棕色</button>
                    <button class="spec-option border-2 border-gray-600 px-4 py-2 rounded-lg text-sm" data-type="color" data-value="黑色">黑色</button>
                </div>
            </div>
            
            <!-- Weight Selection -->
            <div class="mb-6">
                <h4 class="font-bold mb-3">重量</h4>
                <div class="flex gap-3">
                    <button class="spec-option border-2 border-gray-600 px-4 py-2 rounded-lg text-sm" data-type="weight" data-value="18oz">18oz</button>
                    <button class="spec-option border-2 border-gray-600 px-4 py-2 rounded-lg text-sm" data-type="weight" data-value="19oz">19oz</button>
                    <button class="spec-option border-2 border-gray-600 px-4 py-2 rounded-lg text-sm" data-type="weight" data-value="20oz">20oz</button>
                </div>
            </div>
            
            <!-- Quantity -->
            <div class="mb-6">
                <h4 class="font-bold mb-3">数量</h4>
                <div class="flex items-center gap-3">
                    <button onclick="changeQuantity(-1)" class="w-10 h-10 border border-gray-600 rounded-lg flex items-center justify-center">
                        <i class="fa-solid fa-minus"></i>
                    </button>
                    <span class="text-lg font-bold" id="quantity">1</span>
                    <button onclick="changeQuantity(1)" class="w-10 h-10 border border-gray-600 rounded-lg flex items-center justify-center">
                        <i class="fa-solid fa-plus"></i>
                    </button>
                </div>
            </div>
            
            <!-- Confirm Button -->
            <button onclick="confirmSpec()" class="w-full bg-gradient-to-r from-yellow-400 to-orange-500 text-black py-3 rounded-lg font-bold">
                确定
            </button>
        </div>
    </div>

    <!-- Share Modal -->
    <div class="fixed inset-0 bg-black/50 z-50 hidden flex items-center justify-center" id="shareOverlay" onclick="closeShareModal()">
        <div class="share-modal bg-gray-900 rounded-2xl p-6 mx-4 max-w-sm w-full" onclick="event.stopPropagation()">
            <div class="text-center mb-6">
                <h3 class="text-lg font-bold mb-2">分享商品</h3>
                <p class="text-sm text-gray-400">分享给好友，一起来看看这个好物</p>
            </div>
            
            <!-- Share Card Preview -->
            <div class="bg-white rounded-xl p-4 mb-6">
                <div class="flex gap-3">
                    <img src="https://images.unsplash.com/photo-1557996739-b883c79d1297?q=80&w=100&auto=format&fit=crop" class="w-16 h-16 rounded-lg object-cover" alt="Product">
                    <div class="flex-1">
                        <p class="text-black font-bold text-sm">Cuppa-JR 职业级枫木球杆</p>
                        <p class="text-red-500 font-bold text-lg">¥1280</p>
                    </div>
                </div>
                <div class="mt-3 text-center">
                    <div class="w-16 h-16 bg-gray-200 rounded-lg mx-auto mb-2 flex items-center justify-center">
                        <i class="fa-solid fa-qrcode text-2xl text-gray-600"></i>
                    </div>
                    <p class="text-xs text-gray-600">扫码即刻拥有</p>
                </div>
            </div>
            
            <!-- Share Actions -->
            <div class="flex gap-3">
                <button onclick="shareToFriend()" class="flex-1 bg-green-600 text-white py-3 rounded-lg font-bold">
                    发送给好友
                </button>
                <button onclick="saveImage()" class="flex-1 bg-gray-700 text-white py-3 rounded-lg font-bold">
                    保存图片
                </button>
            </div>
        </div>
    </div>

    <script>
        let currentImageIndex = 0;
        let selectedSpecs = {
            color: '原木色',
            weight: null
        };
        let quantity = 1;

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            setupImageCarousel();
            setupSpecSelection();
        });

        // 图片轮播功能
        function setupImageCarousel() {
            const carousel = document.getElementById('imageCarousel');
            let startX = 0;
            let currentX = 0;
            let isDragging = false;

            carousel.addEventListener('touchstart', (e) => {
                startX = e.touches[0].clientX;
                isDragging = true;
            });

            carousel.addEventListener('touchmove', (e) => {
                if (!isDragging) return;
                currentX = e.touches[0].clientX;
                const diffX = currentX - startX;
                
                if (Math.abs(diffX) > 50) {
                    if (diffX > 0 && currentImageIndex > 0) {
                        currentImageIndex--;
                        updateImageCarousel();
                    } else if (diffX < 0 && currentImageIndex < 2) {
                        currentImageIndex++;
                        updateImageCarousel();
                    }
                    isDragging = false;
                }
            });

            carousel.addEventListener('touchend', () => {
                isDragging = false;
            });
        }

        function updateImageCarousel() {
            const carousel = document.getElementById('imageCarousel');
            carousel.style.transform = `translateX(-${currentImageIndex * 100}%)`;
            
            // 更新指示器
            for (let i = 0; i < 3; i++) {
                const indicator = document.getElementById(`img-indicator-${i}`);
                if (i === currentImageIndex) {
                    indicator.className = 'w-2 h-2 rounded-full bg-white';
                } else {
                    indicator.className = 'w-2 h-2 rounded-full bg-white/50';
                }
            }
        }

        // 规格选择功能
        function setupSpecSelection() {
            document.querySelectorAll('.spec-option').forEach(option => {
                option.addEventListener('click', function() {
                    const type = this.dataset.type;
                    const value = this.dataset.value;
                    
                    // 更新选中状态
                    document.querySelectorAll(`[data-type="${type}"]`).forEach(opt => {
                        opt.className = 'spec-option border-2 border-gray-600 px-4 py-2 rounded-lg text-sm';
                    });
                    this.className = 'spec-option border-2 border-yellow-400 bg-yellow-400/20 px-4 py-2 rounded-lg text-sm';
                    
                    selectedSpecs[type] = value;
                    updateSpecDisplay();
                });
            });
        }

        function updateSpecDisplay() {
            const specText = `已选择 ${selectedSpecs.color || '颜色'} ${selectedSpecs.weight || '重量'}`;
            document.getElementById('selectedSpec').textContent = specText;
        }

        // Modal 控制函数
        function openSpecModal() {
            const overlay = document.getElementById('specOverlay');
            const modal = overlay.querySelector('.spec-modal');
            
            overlay.classList.remove('hidden');
            setTimeout(() => modal.classList.add('active'), 10);
        }

        function closeSpecModal() {
            const overlay = document.getElementById('specOverlay');
            const modal = overlay.querySelector('.spec-modal');
            
            modal.classList.remove('active');
            setTimeout(() => overlay.classList.add('hidden'), 300);
        }

        function openShare() {
            const overlay = document.getElementById('shareOverlay');
            const modal = overlay.querySelector('.share-modal');
            
            overlay.classList.remove('hidden');
            setTimeout(() => modal.classList.add('active'), 10);
        }

        function closeShareModal() {
            const overlay = document.getElementById('shareOverlay');
            const modal = overlay.querySelector('.share-modal');
            
            modal.classList.remove('active');
            setTimeout(() => overlay.classList.add('hidden'), 300);
        }

        // 数量控制
        function changeQuantity(delta) {
            quantity = Math.max(1, quantity + delta);
            document.getElementById('quantity').textContent = quantity;
        }

        function confirmSpec() {
            if (!selectedSpecs.color || !selectedSpecs.weight) {
                alert('请选择完整的规格');
                return;
            }
            updateSpecDisplay();
            closeSpecModal();
        }

        // 功能函数
        function previewImages() {
            alert('图片预览功能');
        }

        function addToCart() {
            if (!selectedSpecs.color || !selectedSpecs.weight) {
                openSpecModal();
                return;
            }
            alert('已添加到购物车');
        }

        function buyNow() {
            if (!selectedSpecs.color || !selectedSpecs.weight) {
                openSpecModal();
                return;
            }
            window.location.href = 'checkout.html';
        }

        function contactService() {
            alert('联系客服');
        }

        function openCart() {
            window.location.href = 'cart.html';
        }

        function shareToFriend() {
            alert('分享给好友');
            closeShareModal();
        }

        function saveImage() {
            alert('图片已保存到相册');
            closeShareModal();
        }
    </script>

</body>
</html>
