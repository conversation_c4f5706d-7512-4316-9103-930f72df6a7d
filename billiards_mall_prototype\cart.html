<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>购物车</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #1A1A1A; color: white; font-family: sans-serif; }
        .glow-yellow { box-shadow: 0 0 15px rgba(255, 215, 0, 0.5); }
    </style>
</head>
<body class="pb-32">

    <!-- iOS Status Bar -->
    <div class="fixed top-0 left-0 right-0 h-11 bg-black/30 backdrop-blur-sm z-50 px-4 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
        <div class="flex items-center gap-2"><i class="fa-solid fa-signal"></i><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
    </div>
    
    <!-- Header -->
    <div class="fixed top-11 left-0 right-0 bg-gray-900/90 backdrop-blur-md z-40 px-4 py-3 border-b border-gray-700">
        <div class="flex items-center justify-between">
            <div class="flex items-center gap-3">
                <button onclick="history.back()" class="text-white"><i class="fa-solid fa-arrow-left text-xl"></i></button>
                <h1 class="text-lg font-bold">购物车</h1>
            </div>
            <button onclick="editMode()" class="text-yellow-400" id="editBtn">编辑</button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="pt-24">
        <!-- Cart Items -->
        <div class="space-y-3" id="cartItems">
            <!-- Cart Item 1 -->
            <div class="bg-gray-800 mx-4 rounded-xl p-4">
                <div class="flex items-start gap-3">
                    <input type="checkbox" class="item-checkbox mt-2 w-5 h-5 text-yellow-400 rounded" checked>
                    <img src="https://images.unsplash.com/photo-1557996739-b883c79d1297?q=80&w=100&auto=format&fit=crop" class="w-20 h-20 rounded-lg object-cover" alt="Product">
                    <div class="flex-1">
                        <h3 class="font-bold text-sm">Cuppa-JR 职业级枫木球杆</h3>
                        <p class="text-xs text-gray-400 mt-1">规格：原木色 19oz</p>
                        <div class="flex items-center justify-between mt-3">
                            <p class="text-lg font-bold text-yellow-400">¥1280</p>
                            <div class="flex items-center gap-3">
                                <button onclick="changeQuantity(1, -1)" class="w-8 h-8 border border-gray-600 rounded-lg flex items-center justify-center">
                                    <i class="fa-solid fa-minus text-xs"></i>
                                </button>
                                <span class="font-bold" id="qty-1">1</span>
                                <button onclick="changeQuantity(1, 1)" class="w-8 h-8 border border-gray-600 rounded-lg flex items-center justify-center">
                                    <i class="fa-solid fa-plus text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <button onclick="removeItem(1)" class="text-gray-400 hidden" id="remove-1">
                        <i class="fa-solid fa-trash"></i>
                    </button>
                </div>
            </div>

            <!-- Cart Item 2 -->
            <div class="bg-gray-800 mx-4 rounded-xl p-4">
                <div class="flex items-start gap-3">
                    <input type="checkbox" class="item-checkbox mt-2 w-5 h-5 text-yellow-400 rounded" checked>
                    <img src="https://images.unsplash.com/photo-1631181827493-276949033331?q=80&w=100&auto=format&fit=crop" class="w-20 h-20 rounded-lg object-cover" alt="Product">
                    <div class="flex-1">
                        <h3 class="font-bold text-sm">进口比利时水晶球 标准版</h3>
                        <p class="text-xs text-gray-400 mt-1">规格：16颗装</p>
                        <div class="flex items-center justify-between mt-3">
                            <p class="text-lg font-bold text-yellow-400">¥899</p>
                            <div class="flex items-center gap-3">
                                <button onclick="changeQuantity(2, -1)" class="w-8 h-8 border border-gray-600 rounded-lg flex items-center justify-center">
                                    <i class="fa-solid fa-minus text-xs"></i>
                                </button>
                                <span class="font-bold" id="qty-2">1</span>
                                <button onclick="changeQuantity(2, 1)" class="w-8 h-8 border border-gray-600 rounded-lg flex items-center justify-center">
                                    <i class="fa-solid fa-plus text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <button onclick="removeItem(2)" class="text-gray-400 hidden" id="remove-2">
                        <i class="fa-solid fa-trash"></i>
                    </button>
                </div>
            </div>

            <!-- Cart Item 3 -->
            <div class="bg-gray-800 mx-4 rounded-xl p-4">
                <div class="flex items-start gap-3">
                    <input type="checkbox" class="item-checkbox mt-2 w-5 h-5 text-yellow-400 rounded">
                    <img src="https://plus.unsplash.com/premium_photo-1661662991873-6784a0c897f2?q=80&w=100&auto=format&fit=crop" class="w-20 h-20 rounded-lg object-cover" alt="Product">
                    <div class="flex-1">
                        <h3 class="font-bold text-sm">专业防滑台球手套</h3>
                        <p class="text-xs text-gray-400 mt-1">规格：L码</p>
                        <div class="flex items-center justify-between mt-3">
                            <p class="text-lg font-bold text-yellow-400">¥49</p>
                            <div class="flex items-center gap-3">
                                <button onclick="changeQuantity(3, -1)" class="w-8 h-8 border border-gray-600 rounded-lg flex items-center justify-center">
                                    <i class="fa-solid fa-minus text-xs"></i>
                                </button>
                                <span class="font-bold" id="qty-3">2</span>
                                <button onclick="changeQuantity(3, 1)" class="w-8 h-8 border border-gray-600 rounded-lg flex items-center justify-center">
                                    <i class="fa-solid fa-plus text-xs"></i>
                                </button>
                            </div>
                        </div>
                    </div>
                    <button onclick="removeItem(3)" class="text-gray-400 hidden" id="remove-3">
                        <i class="fa-solid fa-trash"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Recommended Products -->
        <div class="mt-8 px-4">
            <h3 class="font-bold text-lg mb-4">为你推荐</h3>
            <div class="grid grid-cols-2 gap-4">
                <div class="bg-gray-800 rounded-xl overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1594759881342-a059d18e5898?q=80&w=300&auto=format&fit=crop" class="w-full h-32 object-cover" alt="Product">
                    <div class="p-3">
                        <p class="text-sm font-semibold">大师级巧克粉</p>
                        <p class="text-lg font-bold text-yellow-400 mt-1">¥128</p>
                        <button onclick="addRecommended(4)" class="w-full mt-2 bg-yellow-400 text-black py-2 rounded-lg text-sm font-bold">
                            加入购物车
                        </button>
                    </div>
                </div>
                <div class="bg-gray-800 rounded-xl overflow-hidden">
                    <img src="https://images.unsplash.com/photo-1574116035011-e68651815f43?q=80&w=300&auto=format&fit=crop" class="w-full h-32 object-cover" alt="Product">
                    <div class="p-3">
                        <p class="text-sm font-semibold">碳纤维球杆头</p>
                        <p class="text-lg font-bold text-yellow-400 mt-1">¥299</p>
                        <button onclick="addRecommended(5)" class="w-full mt-2 bg-yellow-400 text-black py-2 rounded-lg text-sm font-bold">
                            加入购物车
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Empty Cart State (Hidden by default) -->
        <div class="hidden text-center py-20" id="emptyCart">
            <i class="fa-solid fa-shopping-cart text-6xl text-gray-600 mb-4"></i>
            <p class="text-gray-400 text-lg mb-4">购物车空空如也</p>
            <button onclick="goShopping()" class="bg-yellow-400 text-black px-8 py-3 rounded-lg font-bold">
                去逛逛
            </button>
        </div>
    </div>

    <!-- Bottom Checkout Bar -->
    <div class="fixed bottom-0 left-0 right-0 bg-gray-900/90 backdrop-blur-md border-t border-gray-700 p-4">
        <div class="flex items-center gap-4">
            <label class="flex items-center gap-2">
                <input type="checkbox" id="selectAll" class="w-5 h-5 text-yellow-400 rounded" checked>
                <span class="text-sm">全选</span>
            </label>
            <div class="flex-1 text-right">
                <p class="text-sm text-gray-400">合计：</p>
                <p class="text-xl font-bold text-yellow-400">¥<span id="totalPrice">2179</span></p>
            </div>
            <button onclick="checkout()" class="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-8 py-3 rounded-lg font-bold glow-yellow" id="checkoutBtn">
                结算(<span id="selectedCount">2</span>)
            </button>
        </div>
    </div>

    <script>
        let cartItems = [
            { id: 1, name: "Cuppa-JR 职业级枫木球杆", price: 1280, quantity: 1, selected: true },
            { id: 2, name: "进口比利时水晶球 标准版", price: 899, quantity: 1, selected: true },
            { id: 3, name: "专业防滑台球手套", price: 49, quantity: 2, selected: false }
        ];

        let isEditMode = false;

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            updateCartDisplay();
            setupEventListeners();
        });

        function setupEventListeners() {
            // 全选复选框
            document.getElementById('selectAll').addEventListener('change', function() {
                const isChecked = this.checked;
                cartItems.forEach(item => item.selected = isChecked);
                document.querySelectorAll('.item-checkbox').forEach(cb => cb.checked = isChecked);
                updateCartDisplay();
            });

            // 单个商品复选框
            document.querySelectorAll('.item-checkbox').forEach((checkbox, index) => {
                checkbox.addEventListener('change', function() {
                    cartItems[index].selected = this.checked;
                    updateCartDisplay();
                });
            });
        }

        function updateCartDisplay() {
            const selectedItems = cartItems.filter(item => item.selected);
            const totalPrice = selectedItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
            const selectedCount = selectedItems.length;

            document.getElementById('totalPrice').textContent = totalPrice;
            document.getElementById('selectedCount').textContent = selectedCount;

            // 更新全选状态
            const allSelected = cartItems.length > 0 && cartItems.every(item => item.selected);
            document.getElementById('selectAll').checked = allSelected;

            // 更新结算按钮状态
            const checkoutBtn = document.getElementById('checkoutBtn');
            if (selectedCount === 0) {
                checkoutBtn.className = 'bg-gray-600 text-gray-400 px-8 py-3 rounded-lg font-bold cursor-not-allowed';
                checkoutBtn.disabled = true;
            } else {
                checkoutBtn.className = 'bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-8 py-3 rounded-lg font-bold glow-yellow';
                checkoutBtn.disabled = false;
            }
        }

        function changeQuantity(itemId, delta) {
            const item = cartItems.find(item => item.id === itemId);
            if (item) {
                item.quantity = Math.max(1, item.quantity + delta);
                document.getElementById(`qty-${itemId}`).textContent = item.quantity;
                updateCartDisplay();
            }
        }

        function removeItem(itemId) {
            cartItems = cartItems.filter(item => item.id !== itemId);
            
            // 移除DOM元素
            const itemElement = document.querySelector(`#remove-${itemId}`).closest('.bg-gray-800');
            itemElement.remove();
            
            updateCartDisplay();
            
            // 如果购物车为空，显示空状态
            if (cartItems.length === 0) {
                document.getElementById('cartItems').classList.add('hidden');
                document.getElementById('emptyCart').classList.remove('hidden');
            }
        }

        function editMode() {
            isEditMode = !isEditMode;
            const editBtn = document.getElementById('editBtn');
            const removeButtons = document.querySelectorAll('[id^="remove-"]');
            
            if (isEditMode) {
                editBtn.textContent = '完成';
                removeButtons.forEach(btn => btn.classList.remove('hidden'));
            } else {
                editBtn.textContent = '编辑';
                removeButtons.forEach(btn => btn.classList.add('hidden'));
            }
        }

        function addRecommended(productId) {
            alert('商品已添加到购物车');
        }

        function checkout() {
            const selectedItems = cartItems.filter(item => item.selected);
            if (selectedItems.length === 0) {
                alert('请选择要结算的商品');
                return;
            }
            window.location.href = 'checkout.html';
        }

        function goShopping() {
            window.location.href = 'mall_list_enhanced.html';
        }
    </script>

</body>
</html>
