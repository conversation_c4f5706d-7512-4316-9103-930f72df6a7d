<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>同城打球</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #1A1A1A; color: white; font-family: sans-serif; }
    </style>
</head>
<body class="pb-24">

    <!-- iOS Status Bar & App Header -->
    <div class="fixed top-0 left-0 right-0 bg-gray-900/80 backdrop-blur-md z-50">
        <div class="h-11 px-4 flex justify-between items-center text-sm font-semibold">
            <span>9:41</span>
             <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
            <div class="flex items-center gap-2">
                <i class="fa-solid fa-signal"></i>
                <i class="fa-solid fa-wifi"></i>
                <i class="fa-solid fa-battery-full"></i>
            </div>
        </div>
        <div class="h-12 flex items-center justify-center relative">
            <h1 class="font-bold text-lg">同城打球</h1>
            <button class="absolute right-4 text-gray-300 bg-gray-700 px-3 py-1 rounded-full text-xs">筛选</button>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="pt-24 px-4">
        <!-- Empty State -->
        <div class="text-center py-16">
            <div class="w-32 h-32 bg-gray-800 rounded-full mx-auto flex items-center justify-center">
                <div class="w-24 h-24 bg-gray-700 rounded-full flex items-center justify-center">
                    <div class="w-8 h-8 bg-yellow-400 rounded-full"></div>
                </div>
            </div>
            <p class="mt-6 text-lg font-bold">同城还没有人发起打球, 立即发布</p>
            <p class="text-gray-400 mt-2 text-sm">快成为您所在城市的打球第一人吧~</p>
            <button class="mt-6 bg-white text-black font-bold py-3 px-12 rounded-full shadow-lg">发布打球</button>
        </div>

        <!-- News/Event Card -->
        <div class="mt-8">
            <h2 class="text-xl font-bold mb-3">小铁新鲜事</h2>
            <div class="bg-gradient-to-br from-yellow-400 to-orange-500 rounded-2xl p-1 relative">
                <div class="absolute top-2 right-2 bg-green-500 text-white text-xs font-bold px-2 py-0.5 rounded-md">官方赛事</div>
                 <img src="https://images.unsplash.com/photo-1611094628143-6c8a3299725f?q=80&w=800&auto=format&fit=crop" class="absolute inset-0 w-full h-full object-cover rounded-2xl opacity-20">
                <div class="relative z-10 p-4 text-center">
                     <p class="text-black/70 text-sm font-semibold">第二届中式台球民间球王赛</p>
                    <div class="my-4 border-2 border-black/80 bg-white/50 rounded-lg p-2 relative">
                        <span class="absolute -top-3 left-2 bg-red-600 text-white text-xs font-bold px-1.5 rounded-sm">美团</span>
                         <h3 class="text-3xl text-black font-black leading-tight drop-shadow-sm">100万奖池等你来</h3>
                    </div>
                     <button class="w-full bg-gray-800/80 backdrop-blur-sm text-white font-bold py-3 rounded-full shadow-lg">
                        了解详情 <i class="fa-solid fa-chevron-right ml-2 text-xs"></i>
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bottom Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 h-20 bg-gray-900/80 backdrop-blur-md flex justify-around items-center text-gray-400 border-t border-gray-700">
         <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-house text-xl"></i><span class="text-xs">首页</span></a>
        <a href="#" class="flex flex-col items-center gap-1 text-yellow-400"><i class="fa-solid fa-users text-xl"></i><span class="text-xs font-bold">同城</span></a>
        <a href="#" class="w-16 h-16 -mt-8 bg-white/20 backdrop-blur-lg border-2 border-white/30 rounded-full flex items-center justify-center text-white"><i class="fa-solid fa-qrcode text-3xl"></i></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-file-invoice text-xl"></i><span class="text-xs">订单</span></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-user text-xl"></i><span class="text-xs">我的</span></a>
    </div>

</body>
</html>