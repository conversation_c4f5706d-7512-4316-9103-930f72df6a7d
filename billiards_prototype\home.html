<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #1A1A1A; color: white; font-family: sans-serif; }
        .glow-yellow { box-shadow: 0 0 15px rgba(255, 215, 0, 0.5); }
    </style>
</head>
<body class="pb-24">

    <!-- iOS Status Bar -->
    <div class="fixed top-0 left-0 right-0 h-11 bg-black/30 backdrop-blur-sm z-50 px-4 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
        <div class="flex items-center gap-2">
            <i class="fa-solid fa-signal"></i>
            <i class="fa-solid fa-wifi"></i>
            <i class="fa-solid fa-battery-full"></i>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="pt-11">
        <!-- Hero Banner -->
        <div class="relative h-60 bg-gradient-to-br from-green-400 via-yellow-400 to-yellow-500 overflow-hidden">
            <!-- Background elements -->
            <img src="https://images.unsplash.com/photo-1601758178236-43588a45e41a?q=80&w=800&auto=format&fit=crop" class="absolute inset-0 w-full h-full object-cover opacity-20">
            <span class="absolute text-8xl opacity-10 top-5 -left-5 transform -rotate-12">🎉</span>
            <span class="absolute text-6xl opacity-15 bottom-5 right-5 transform rotate-12">🎱</span>

            <!-- Robot Mascot - Using Font Awesome as placeholder -->
            <div class="absolute right-20 bottom-0">
                <i class="fa-solid fa-robot text-9xl text-yellow-300" style="text-shadow: 2px 2px 10px rgba(0,0,0,0.5);"></i>
                <div class="absolute -top-2 -right-2 w-8 h-8 bg-yellow-400 rounded-full flex items-center justify-center font-bold text-black animate-pulse">?</div>
            </div>

            <!-- Promotion Text -->
            <div class="absolute top-8 left-4">
                <p class="bg-red-600 text-white text-xs font-bold px-2 py-1 rounded-full inline-block">限时参与 送完即止</p>
                <h1 class="text-4xl font-black mt-2 drop-shadow-lg" style="font-family: 'Heiti SC', 'Microsoft YaHei', sans-serif;">你<span class="text-yellow-300">有</span>一次</h1>
                <h2 class="text-5xl font-black text-yellow-300 drop-shadow-lg" style="font-family: 'Heiti SC', 'Microsoft YaHei', sans-serif;">免单机会</h2>
                <button class="mt-4 bg-white text-black font-bold py-3 px-8 rounded-full text-lg shadow-xl">立即领取</button>
            </div>
            
            <!-- Ticket -->
            <div class="absolute right-4 bottom-8 bg-orange-400 w-24 h-36 rounded-xl shadow-2xl transform rotate-6 border-4 border-white flex flex-col items-center justify-center">
                 <div class="w-full h-1 bg-white/50 border-t-2 border-b-2 border-dashed border-white"></div>
                 <span class="text-5xl font-black text-white py-2">免</span>
                 <span class="text-5xl font-black text-white py-2">单</span>
                 <div class="w-full h-1 bg-white/50 border-t-2 border-b-2 border-dashed border-white"></div>
            </div>
        </div>

        <!-- User Profile Card -->
        <div class="p-4 mx-4 -mt-8 bg-gray-800 rounded-2xl shadow-lg relative z-10">
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-3">
                    <img src="https://i.pravatar.cc/50?u=a042581f4e29026704d" alt="avatar" class="w-12 h-12 rounded-full border-2 border-yellow-400">
                    <div>
                        <p class="font-bold text-lg">明亮的杰瑞 <i class="fa-solid fa-feather text-blue-400"></i></p>
                        <div class="text-xs text-gray-400 flex items-center gap-1 mt-1">
                            <span class="bg-yellow-500 text-black font-bold px-1.5 py-0.5 rounded">LV1·萌新</span>
                            <span>成长值 0/1 ></span>
                        </div>
                    </div>
                </div>
                <div>
                    <p class="text-lg font-bold">¥ <span class="text-yellow-400">0</span></p>
                    <p class="text-xs text-gray-400 text-right">会员余额</p>
                </div>
                 <div>
                    <p class="text-lg font-bold"><span class="text-yellow-400">0</span> 张</p>
                    <p class="text-xs text-gray-400 text-right">优惠券</p>
                </div>
            </div>
            <div class="mt-3 bg-black/30 p-2 rounded-lg flex justify-between items-center">
                <p class="text-sm font-bold"><i class="fa-solid fa-crown text-yellow-400"></i> UVIP · 首单立减10元, 2单回本!</p>
                <button class="bg-gradient-to-r from-yellow-400 to-orange-500 text-black text-xs font-bold py-1.5 px-3 rounded-full">限时领取</button>
            </div>
        </div>

        <!-- Function Grid -->
        <div class="grid grid-cols-2 gap-4 p-4 mt-2">
            <!-- Nearby Stores -->
            <div class="bg-gray-800 rounded-2xl p-4 flex flex-col justify-between h-48 relative overflow-hidden">
                <div>
                    <h3 class="text-2xl font-bold">附近门店</h3>
                    <p class="text-sm text-gray-400 mt-1"><i class="fa-solid fa-location-dot"></i> 5km | 厦门思明101聚...</p>
                </div>
                <div class="absolute -bottom-8 -right-8 w-40 h-40 bg-green-500 rounded-full opacity-30"></div>
                <i class="fa-solid fa-map-location-dot text-8xl text-green-400 absolute bottom-4 right-4 opacity-80"></i>
                <button class="bg-white/90 text-black font-bold py-2 px-6 rounded-full self-start mt-2">立即打球</button>
            </div>
            <!-- Scan QR -->
            <div class="bg-gray-800 rounded-2xl p-4 flex flex-col">
                <h3 class="text-2xl font-bold">扫码开台</h3>
                <p class="text-sm text-gray-400 mt-1">请扫描桌上的二维码</p>
                <div class="flex-grow flex items-center justify-center">
                    <i class="fa-solid fa-qrcode text-8xl text-yellow-400"></i>
                </div>
            </div>
            <!-- More ways to play -->
            <div class="bg-gradient-to-br from-green-500 to-teal-600 rounded-2xl p-4 relative h-32 flex flex-col justify-end">
                <img src="https://images.unsplash.com/photo-1559523161-0d5b3d99d32b?q=80&w=800&auto=format&fit=crop" class="absolute inset-0 w-full h-full object-cover rounded-2xl opacity-30">
                <h3 class="text-2xl font-bold">追分玩法</h3>
                <div class="flex items-center gap-1 mt-1 text-sm">
                    <div class="flex -space-x-2">
                        <img src="https://i.pravatar.cc/24?u=1" class="w-6 h-6 rounded-full border-2 border-white">
                        <img src="https://i.pravatar.cc/24?u=2" class="w-6 h-6 rounded-full border-2 border-white">
                        <img src="https://i.pravatar.cc/24?u=3" class="w-6 h-6 rounded-full border-2 border-white">
                    </div>
                    <p>99+人等你追分</p>
                </div>
            </div>
             <!-- Other Functions -->
             <div class="bg-gradient-to-br from-orange-500 to-yellow-500 rounded-2xl p-4 flex flex-col justify-end h-32 relative">
                <div class="absolute top-2 right-2 flex gap-2">
                    <i class="fa-brands fa-tiktok text-xl text-white/80"></i>
                </div>
                <h3 class="text-2xl font-bold">团购核销</h3>
                <p class="text-sm text-white/80 mt-1">抖音/美团/点评/快手</p>
            </div>
        </div>
    </div>
    
    <!-- Bottom Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 h-20 bg-gray-900/80 backdrop-blur-md flex justify-around items-center text-gray-400 border-t border-gray-700">
        <a href="#" class="flex flex-col items-center gap-1 text-yellow-400">
            <i class="fa-solid fa-house text-xl"></i>
            <span class="text-xs font-bold">首页</span>
        </a>
        <a href="#" class="flex flex-col items-center gap-1">
            <i class="fa-solid fa-store text-xl"></i>
            <span class="text-xs">门店</span>
        </a>
        <a href="#" class="w-16 h-16 -mt-8 bg-yellow-400 rounded-full flex items-center justify-center text-black shadow-lg glow-yellow">
             <i class="fa-solid fa-coins text-3xl"></i>
        </a>
        <a href="#" class="flex flex-col items-center gap-1">
            <i class="fa-solid fa-file-invoice text-xl"></i>
            <span class="text-xs">订单</span>
        </a>
        <a href="#" class="flex flex-col items-center gap-1">
            <i class="fa-solid fa-user text-xl"></i>
            <span class="text-xs">我的</span>
        </a>
    </div>

</body>
</html>