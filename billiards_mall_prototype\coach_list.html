<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教练列表</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #1A1A1A; color: white; font-family: sans-serif; }
        .coach-card {
            background: linear-gradient(135deg, #2D3748 0%, #1A202C 100%);
            border: 1px solid #4A5568;
        }
        .coach-card:hover {
            border-color: #F6E05E;
            transform: translateY(-2px);
            transition: all 0.3s ease;
        }
        .rating-stars {
            color: #F6E05E;
        }
        .certification-badge {
            background: linear-gradient(135deg, #F6E05E 0%, #D69E2E 100%);
            color: #1A202C;
        }
    </style>
</head>
<body class="pb-24">

    <!-- iOS Status Bar -->
    <div class="fixed top-0 left-0 right-0 h-11 bg-black/30 backdrop-blur-sm z-50 px-4 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
        <div class="flex items-center gap-2"><i class="fa-solid fa-signal"></i><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
    </div>
    
    <!-- Header -->
    <div class="fixed top-11 left-0 right-0 bg-gray-900/90 backdrop-blur-md z-40 px-4 py-3 border-b border-gray-700">
        <div class="flex items-center gap-3">
            <button onclick="history.back()" class="text-white"><i class="fa-solid fa-arrow-left text-xl"></i></button>
            <h1 class="text-lg font-bold flex-1">专业教练</h1>
            <button onclick="openFilter()" class="text-white"><i class="fa-solid fa-filter text-xl"></i></button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="pt-24">
        <!-- Filter Tabs -->
        <div class="bg-gray-800 px-4 py-3 sticky top-24 z-30">
            <div class="flex gap-4 overflow-x-auto">
                <button class="filter-tab bg-yellow-400 text-black px-4 py-2 rounded-full text-sm font-bold whitespace-nowrap" data-filter="all">全部教练</button>
                <button class="filter-tab bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap" data-filter="snooker">斯诺克</button>
                <button class="filter-tab bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap" data-filter="chinese">中式八球</button>
                <button class="filter-tab bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap" data-filter="nine-ball">九球</button>
                <button class="filter-tab bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap" data-filter="available">可预约</button>
            </div>
        </div>

        <!-- Coach List -->
        <div class="p-4 space-y-4" id="coachList">
            <!-- Coach Card 1 -->
            <div class="coach-card rounded-2xl p-4 cursor-pointer" onclick="openCoachDetail(1)">
                <div class="flex gap-4">
                    <!-- Coach Avatar -->
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=150&auto=format&fit=crop" class="w-20 h-20 rounded-full object-cover border-2 border-yellow-400" alt="Coach Avatar">
                        <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-gray-800 flex items-center justify-center">
                            <i class="fa-solid fa-check text-xs text-white"></i>
                        </div>
                    </div>
                    
                    <!-- Coach Info -->
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <div>
                                <h3 class="text-lg font-bold">张教练</h3>
                                <div class="certification-badge inline-block px-2 py-1 rounded-full text-xs font-bold mt-1">
                                    国家一级教练
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="flex items-center gap-1 rating-stars">
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <span class="text-white text-sm ml-1">5.0</span>
                                </div>
                                <p class="text-xs text-gray-400 mt-1">128条评价</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center gap-4 mb-2">
                            <span class="bg-blue-600 text-white px-2 py-1 rounded text-xs">斯诺克</span>
                            <span class="bg-green-600 text-white px-2 py-1 rounded text-xs">中式八球</span>
                        </div>
                        
                        <p class="text-sm text-gray-300 mb-3">10年教学经验，擅长技术分析和心理指导，帮助学员快速提升球技</p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4 text-sm text-gray-400">
                                <span><i class="fa-solid fa-clock"></i> 今日可约</span>
                                <span><i class="fa-solid fa-location-dot"></i> 思明店</span>
                            </div>
                            <div class="text-right">
                                <p class="text-yellow-400 font-bold">¥180/小时</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coach Card 2 -->
            <div class="coach-card rounded-2xl p-4 cursor-pointer" onclick="openCoachDetail(2)">
                <div class="flex gap-4">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=150&auto=format&fit=crop" class="w-20 h-20 rounded-full object-cover border-2 border-yellow-400" alt="Coach Avatar">
                        <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-gray-800 flex items-center justify-center">
                            <i class="fa-solid fa-check text-xs text-white"></i>
                        </div>
                    </div>
                    
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <div>
                                <h3 class="text-lg font-bold">李教练</h3>
                                <div class="certification-badge inline-block px-2 py-1 rounded-full text-xs font-bold mt-1">
                                    国家二级教练
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="flex items-center gap-1 rating-stars">
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-regular fa-star"></i>
                                    <span class="text-white text-sm ml-1">4.8</span>
                                </div>
                                <p class="text-xs text-gray-400 mt-1">89条评价</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center gap-4 mb-2">
                            <span class="bg-green-600 text-white px-2 py-1 rounded text-xs">中式八球</span>
                            <span class="bg-purple-600 text-white px-2 py-1 rounded text-xs">九球</span>
                        </div>
                        
                        <p class="text-sm text-gray-300 mb-3">专注中式八球教学，注重基础功训练，耐心细致，深受学员喜爱</p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4 text-sm text-gray-400">
                                <span><i class="fa-solid fa-clock"></i> 明日可约</span>
                                <span><i class="fa-solid fa-location-dot"></i> 湖里店</span>
                            </div>
                            <div class="text-right">
                                <p class="text-yellow-400 font-bold">¥150/小时</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coach Card 3 -->
            <div class="coach-card rounded-2xl p-4 cursor-pointer" onclick="openCoachDetail(3)">
                <div class="flex gap-4">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=150&auto=format&fit=crop" class="w-20 h-20 rounded-full object-cover border-2 border-gray-500" alt="Coach Avatar">
                        <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-gray-500 rounded-full border-2 border-gray-800 flex items-center justify-center">
                            <i class="fa-solid fa-pause text-xs text-white"></i>
                        </div>
                    </div>
                    
                    <div class="flex-1 opacity-75">
                        <div class="flex items-start justify-between mb-2">
                            <div>
                                <h3 class="text-lg font-bold">王教练</h3>
                                <div class="bg-gray-600 text-gray-300 inline-block px-2 py-1 rounded-full text-xs font-bold mt-1">
                                    职业选手
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="flex items-center gap-1 rating-stars">
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <span class="text-white text-sm ml-1">4.9</span>
                                </div>
                                <p class="text-xs text-gray-400 mt-1">256条评价</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center gap-4 mb-2">
                            <span class="bg-blue-600 text-white px-2 py-1 rounded text-xs">斯诺克</span>
                            <span class="bg-purple-600 text-white px-2 py-1 rounded text-xs">九球</span>
                        </div>
                        
                        <p class="text-sm text-gray-300 mb-3">前职业选手，技术精湛，教学严谨，适合有一定基础的学员进阶</p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4 text-sm text-gray-400">
                                <span><i class="fa-solid fa-clock"></i> 暂不可约</span>
                                <span><i class="fa-solid fa-location-dot"></i> 集美店</span>
                            </div>
                            <div class="text-right">
                                <p class="text-gray-400 font-bold">¥280/小时</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Coach Card 4 -->
            <div class="coach-card rounded-2xl p-4 cursor-pointer" onclick="openCoachDetail(4)">
                <div class="flex gap-4">
                    <div class="relative">
                        <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?q=80&w=150&auto=format&fit=crop" class="w-20 h-20 rounded-full object-cover border-2 border-yellow-400" alt="Coach Avatar">
                        <div class="absolute -bottom-1 -right-1 w-6 h-6 bg-green-500 rounded-full border-2 border-gray-800 flex items-center justify-center">
                            <i class="fa-solid fa-check text-xs text-white"></i>
                        </div>
                    </div>
                    
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <div>
                                <h3 class="text-lg font-bold">陈教练</h3>
                                <div class="certification-badge inline-block px-2 py-1 rounded-full text-xs font-bold mt-1">
                                    国家一级教练
                                </div>
                            </div>
                            <div class="text-right">
                                <div class="flex items-center gap-1 rating-stars">
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-solid fa-star"></i>
                                    <i class="fa-regular fa-star"></i>
                                    <span class="text-white text-sm ml-1">4.7</span>
                                </div>
                                <p class="text-xs text-gray-400 mt-1">67条评价</p>
                            </div>
                        </div>
                        
                        <div class="flex items-center gap-4 mb-2">
                            <span class="bg-purple-600 text-white px-2 py-1 rounded text-xs">九球</span>
                            <span class="bg-green-600 text-white px-2 py-1 rounded text-xs">中式八球</span>
                        </div>
                        
                        <p class="text-sm text-gray-300 mb-3">九球专家，曾获得多项比赛冠军，教学风格活泼，深受年轻学员喜爱</p>
                        
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4 text-sm text-gray-400">
                                <span><i class="fa-solid fa-clock"></i> 今日可约</span>
                                <span><i class="fa-solid fa-location-dot"></i> 海沧店</span>
                            </div>
                            <div class="text-right">
                                <p class="text-yellow-400 font-bold">¥200/小时</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 h-20 bg-gray-900/80 backdrop-blur-md flex justify-around items-center text-gray-400 border-t border-gray-700">
        <a href="home_rich.html" class="flex flex-col items-center gap-1"><i class="fa-solid fa-house text-xl"></i><span class="text-xs">首页</span></a>
        <a href="mall_list_enhanced.html" class="flex flex-col items-center gap-1"><i class="fa-solid fa-store text-xl"></i><span class="text-xs">商城</span></a>
        <a href="#" class="w-16 h-16 -mt-8 bg-yellow-400 rounded-full flex items-center justify-center text-black shadow-lg"><i class="fa-solid fa-qrcode text-3xl"></i></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-file-invoice text-xl"></i><span class="text-xs">订单</span></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-user text-xl"></i><span class="text-xs">我的</span></a>
    </div>

    <script>
        // 教练数据
        const coaches = [
            {
                id: 1,
                name: "张教练",
                level: "国家一级教练",
                rating: 5.0,
                reviews: 128,
                specialties: ["斯诺克", "中式八球"],
                description: "10年教学经验，擅长技术分析和心理指导，帮助学员快速提升球技",
                price: 180,
                available: true,
                location: "思明店",
                avatar: "https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=150&auto=format&fit=crop"
            },
            {
                id: 2,
                name: "李教练",
                level: "国家二级教练",
                rating: 4.8,
                reviews: 89,
                specialties: ["中式八球", "九球"],
                description: "专注中式八球教学，注重基础功训练，耐心细致，深受学员喜爱",
                price: 150,
                available: true,
                location: "湖里店",
                avatar: "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=150&auto=format&fit=crop"
            },
            {
                id: 3,
                name: "王教练",
                level: "职业选手",
                rating: 4.9,
                reviews: 256,
                specialties: ["斯诺克", "九球"],
                description: "前职业选手，技术精湛，教学严谨，适合有一定基础的学员进阶",
                price: 280,
                available: false,
                location: "集美店",
                avatar: "https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=150&auto=format&fit=crop"
            },
            {
                id: 4,
                name: "陈教练",
                level: "国家一级教练",
                rating: 4.7,
                reviews: 67,
                specialties: ["九球", "中式八球"],
                description: "九球专家，曾获得多项比赛冠军，教学风格活泼，深受年轻学员喜爱",
                price: 200,
                available: true,
                location: "海沧店",
                avatar: "https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?q=80&w=150&auto=format&fit=crop"
            }
        ];

        let currentFilter = 'all';

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            setupFilterTabs();
        });

        function setupFilterTabs() {
            document.querySelectorAll('.filter-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    const filter = this.dataset.filter;
                    switchFilter(filter);
                });
            });
        }

        function switchFilter(filter) {
            currentFilter = filter;
            
            // 更新Tab样式
            document.querySelectorAll('.filter-tab').forEach(tab => {
                if (tab.dataset.filter === filter) {
                    tab.className = 'filter-tab bg-yellow-400 text-black px-4 py-2 rounded-full text-sm font-bold whitespace-nowrap';
                } else {
                    tab.className = 'filter-tab bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap';
                }
            });
            
            filterCoaches();
        }

        function filterCoaches() {
            let filteredCoaches = [...coaches];
            
            switch (currentFilter) {
                case 'snooker':
                    filteredCoaches = coaches.filter(coach => coach.specialties.includes('斯诺克'));
                    break;
                case 'chinese':
                    filteredCoaches = coaches.filter(coach => coach.specialties.includes('中式八球'));
                    break;
                case 'nine-ball':
                    filteredCoaches = coaches.filter(coach => coach.specialties.includes('九球'));
                    break;
                case 'available':
                    filteredCoaches = coaches.filter(coach => coach.available);
                    break;
                default:
                    filteredCoaches = coaches;
            }
            
            renderCoaches(filteredCoaches);
        }

        function renderCoaches(coachList) {
            // 这里可以重新渲染教练列表，当前示例中直接显示/隐藏现有卡片
            // 实际项目中应该动态生成HTML
            console.log('Filtered coaches:', coachList);
        }

        function openCoachDetail(coachId) {
            window.location.href = `coach_detail.html?id=${coachId}`;
        }

        function openFilter() {
            alert('打开筛选面板');
        }
    </script>

</body>
</html>
