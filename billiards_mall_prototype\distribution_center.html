<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>分销中心</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #1A1A1A; color: white; font-family: sans-serif; }
        .earnings-card {
            background: linear-gradient(135deg, #10B981 0%, #059669 100%);
        }
        .commission-card {
            background: linear-gradient(135deg, #2D3748 0%, #1A202C 100%);
            border: 1px solid #4A5568;
        }
        .glow-green { box-shadow: 0 0 20px rgba(16, 185, 129, 0.5); }
    </style>
</head>
<body class="pb-24">

    <!-- iOS Status Bar -->
    <div class="fixed top-0 left-0 right-0 h-11 bg-black/30 backdrop-blur-sm z-50 px-4 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
        <div class="flex items-center gap-2"><i class="fa-solid fa-signal"></i><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
    </div>
    
    <!-- Header -->
    <div class="fixed top-11 left-0 right-0 bg-gray-900/90 backdrop-blur-md z-40 px-4 py-3 border-b border-gray-700">
        <div class="flex items-center gap-3">
            <button onclick="history.back()" class="text-white"><i class="fa-solid fa-arrow-left text-xl"></i></button>
            <h1 class="text-lg font-bold flex-1">分销中心</h1>
            <button onclick="viewRules()" class="text-green-400">规则说明</button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="pt-24">
        <!-- Earnings Overview -->
        <div class="earnings-card mx-4 rounded-2xl p-6 mb-6 relative overflow-hidden glow-green">
            <div class="absolute top-0 right-0 w-32 h-32 bg-white/10 rounded-full -translate-y-16 translate-x-16"></div>
            <div class="relative z-10">
                <div class="flex items-center justify-between mb-4">
                    <div>
                        <h2 class="text-xl font-bold text-white">我的收益</h2>
                        <p class="text-green-100 text-sm">分享赚钱，轻松获利</p>
                    </div>
                    <i class="fa-solid fa-coins text-3xl text-white"></i>
                </div>
                
                <div class="grid grid-cols-3 gap-4 text-center">
                    <div>
                        <p class="text-2xl font-bold text-white">¥156.80</p>
                        <p class="text-xs text-green-100">累计收益</p>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-white">¥45.20</p>
                        <p class="text-xs text-green-100">本月收益</p>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-white">¥23.60</p>
                        <p class="text-xs text-green-100">可提现</p>
                    </div>
                </div>

                <button onclick="withdraw()" class="w-full bg-white text-green-600 py-3 rounded-xl font-bold mt-4">
                    立即提现
                </button>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="grid grid-cols-3 gap-3 px-4 mb-6">
            <div class="bg-gray-800 rounded-xl p-4 text-center">
                <i class="fa-solid fa-users text-2xl text-blue-400 mb-2"></i>
                <p class="text-lg font-bold text-white">28</p>
                <p class="text-xs text-gray-400">邀请用户</p>
            </div>
            <div class="bg-gray-800 rounded-xl p-4 text-center">
                <i class="fa-solid fa-shopping-cart text-2xl text-green-400 mb-2"></i>
                <p class="text-lg font-bold text-white">156</p>
                <p class="text-xs text-gray-400">成功订单</p>
            </div>
            <div class="bg-gray-800 rounded-xl p-4 text-center">
                <i class="fa-solid fa-percentage text-2xl text-yellow-400 mb-2"></i>
                <p class="text-lg font-bold text-white">15%</p>
                <p class="text-xs text-gray-400">佣金比例</p>
            </div>
        </div>

        <!-- Commission Rules -->
        <div class="px-4 mb-6">
            <h3 class="font-bold text-lg mb-4">佣金规则</h3>
            <div class="space-y-3">
                <div class="commission-card rounded-xl p-4">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-shopping-bag text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-bold">商城购物</h4>
                            <p class="text-sm text-gray-400">推荐用户购买商品，获得订单金额10%佣金</p>
                        </div>
                        <span class="text-green-400 font-bold">10%</span>
                    </div>
                </div>

                <div class="commission-card rounded-xl p-4">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-calendar-check text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-bold">课程预约</h4>
                            <p class="text-sm text-gray-400">推荐用户预约课程，获得课程费用15%佣金</p>
                        </div>
                        <span class="text-green-400 font-bold">15%</span>
                    </div>
                </div>

                <div class="commission-card rounded-xl p-4">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-12 bg-purple-600 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-screwdriver-wrench text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-bold">维修服务</h4>
                            <p class="text-sm text-gray-400">推荐用户使用维修服务，获得服务费8%佣金</p>
                        </div>
                        <span class="text-green-400 font-bold">8%</span>
                    </div>
                </div>

                <div class="commission-card rounded-xl p-4">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center">
                            <i class="fa-solid fa-crown text-white text-xl"></i>
                        </div>
                        <div class="flex-1">
                            <h4 class="font-bold">会员开通</h4>
                            <p class="text-sm text-gray-400">推荐用户开通会员，获得会员费20%佣金</p>
                        </div>
                        <span class="text-green-400 font-bold">20%</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Promotion Tools -->
        <div class="px-4 mb-6">
            <h3 class="font-bold text-lg mb-4">推广工具</h3>
            <div class="grid grid-cols-2 gap-4">
                <button onclick="generatePoster()" class="bg-gray-800 rounded-xl p-4 text-center hover:bg-gray-700 transition-colors">
                    <i class="fa-solid fa-image text-2xl text-blue-400 mb-2"></i>
                    <p class="font-bold text-sm">生成海报</p>
                    <p class="text-xs text-gray-400">专属推广海报</p>
                </button>
                <button onclick="shareLink()" class="bg-gray-800 rounded-xl p-4 text-center hover:bg-gray-700 transition-colors">
                    <i class="fa-solid fa-link text-2xl text-green-400 mb-2"></i>
                    <p class="font-bold text-sm">分享链接</p>
                    <p class="text-xs text-gray-400">专属邀请链接</p>
                </button>
                <button onclick="shareQRCode()" class="bg-gray-800 rounded-xl p-4 text-center hover:bg-gray-700 transition-colors">
                    <i class="fa-solid fa-qrcode text-2xl text-purple-400 mb-2"></i>
                    <p class="font-bold text-sm">二维码</p>
                    <p class="text-xs text-gray-400">扫码注册</p>
                </button>
                <button onclick="shareToSocial()" class="bg-gray-800 rounded-xl p-4 text-center hover:bg-gray-700 transition-colors">
                    <i class="fa-solid fa-share-nodes text-2xl text-yellow-400 mb-2"></i>
                    <p class="font-bold text-sm">社交分享</p>
                    <p class="text-xs text-gray-400">一键分享</p>
                </button>
            </div>
        </div>

        <!-- Recent Earnings -->
        <div class="px-4 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-bold text-lg">最近收益</h3>
                <button onclick="viewAllEarnings()" class="text-green-400 text-sm">查看全部</button>
            </div>
            <div class="space-y-3">
                <div class="bg-gray-800 rounded-xl p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center">
                                <i class="fa-solid fa-plus text-white"></i>
                            </div>
                            <div>
                                <p class="font-bold">课程预约佣金</p>
                                <p class="text-sm text-gray-400">用户：张** · 2024-01-15</p>
                            </div>
                        </div>
                        <span class="text-green-400 font-bold">+¥27.00</span>
                    </div>
                </div>

                <div class="bg-gray-800 rounded-xl p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center">
                                <i class="fa-solid fa-plus text-white"></i>
                            </div>
                            <div>
                                <p class="font-bold">商城购物佣金</p>
                                <p class="text-sm text-gray-400">用户：李** · 2024-01-14</p>
                            </div>
                        </div>
                        <span class="text-green-400 font-bold">+¥12.80</span>
                    </div>
                </div>

                <div class="bg-gray-800 rounded-xl p-4">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-orange-600 rounded-full flex items-center justify-center">
                                <i class="fa-solid fa-plus text-white"></i>
                            </div>
                            <div>
                                <p class="font-bold">会员开通佣金</p>
                                <p class="text-sm text-gray-400">用户：王** · 2024-01-13</p>
                            </div>
                        </div>
                        <span class="text-green-400 font-bold">+¥19.80</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- My Team -->
        <div class="px-4 mb-6">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-bold text-lg">我的团队</h3>
                <button onclick="viewTeam()" class="text-green-400 text-sm">查看详情</button>
            </div>
            <div class="bg-gray-800 rounded-xl p-4">
                <div class="grid grid-cols-2 gap-4 text-center">
                    <div>
                        <p class="text-2xl font-bold text-green-400">28</p>
                        <p class="text-sm text-gray-400">直推用户</p>
                    </div>
                    <div>
                        <p class="text-2xl font-bold text-blue-400">156</p>
                        <p class="text-sm text-gray-400">团队总数</p>
                    </div>
                </div>
                <div class="mt-4 pt-4 border-t border-gray-700">
                    <p class="text-sm text-gray-400 mb-2">本月新增</p>
                    <div class="flex items-center gap-2">
                        <div class="flex -space-x-2">
                            <img src="https://i.pravatar.cc/32?u=1" class="w-8 h-8 rounded-full border-2 border-gray-800" alt="User">
                            <img src="https://i.pravatar.cc/32?u=2" class="w-8 h-8 rounded-full border-2 border-gray-800" alt="User">
                            <img src="https://i.pravatar.cc/32?u=3" class="w-8 h-8 rounded-full border-2 border-gray-800" alt="User">
                        </div>
                        <span class="text-sm text-gray-400">+5人</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="px-4 mb-6">
            <div class="grid grid-cols-2 gap-4">
                <button onclick="inviteFriends()" class="bg-gradient-to-r from-green-400 to-emerald-500 text-white py-4 rounded-xl font-bold">
                    <i class="fa-solid fa-user-plus mr-2"></i>邀请好友
                </button>
                <button onclick="viewEarningsDetail()" class="bg-gray-700 text-white py-4 rounded-xl font-bold">
                    <i class="fa-solid fa-chart-line mr-2"></i>收益明细
                </button>
            </div>
        </div>
    </div>

    <!-- Bottom Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 h-20 bg-gray-900/80 backdrop-blur-md flex justify-around items-center text-gray-400 border-t border-gray-700">
        <a href="home_rich.html" class="flex flex-col items-center gap-1"><i class="fa-solid fa-house text-xl"></i><span class="text-xs">首页</span></a>
        <a href="mall_list_enhanced.html" class="flex flex-col items-center gap-1"><i class="fa-solid fa-store text-xl"></i><span class="text-xs">商城</span></a>
        <a href="#" class="w-16 h-16 -mt-8 bg-yellow-400 rounded-full flex items-center justify-center text-black shadow-lg"><i class="fa-solid fa-qrcode text-3xl"></i></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-file-invoice text-xl"></i><span class="text-xs">订单</span></a>
        <a href="profile_enhanced.html" class="flex flex-col items-center gap-1 text-yellow-400"><i class="fa-solid fa-user text-xl"></i><span class="text-xs font-bold">我的</span></a>
    </div>

    <script>
        function viewRules() {
            alert('分销规则说明页面');
        }

        function withdraw() {
            if (confirm('确定要提现¥23.60吗？')) {
                alert('提现申请已提交，预计1-3个工作日到账');
            }
        }

        function generatePoster() {
            alert('正在生成专属推广海报...');
        }

        function shareLink() {
            const link = 'https://billiards.com/invite?code=ABC123';
            navigator.clipboard.writeText(link).then(() => {
                showNotification('邀请链接已复制到剪贴板');
            });
        }

        function shareQRCode() {
            alert('显示专属二维码');
        }

        function shareToSocial() {
            if (navigator.share) {
                navigator.share({
                    title: '小铁台球 - 专业台球服务',
                    text: '加入小铁台球，享受专业台球服务！',
                    url: 'https://billiards.com/invite?code=ABC123'
                });
            } else {
                shareLink();
            }
        }

        function viewAllEarnings() {
            window.location.href = 'earnings_history.html';
        }

        function viewTeam() {
            window.location.href = 'my_team.html';
        }

        function inviteFriends() {
            shareToSocial();
        }

        function viewEarningsDetail() {
            window.location.href = 'earnings_detail.html';
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-4 py-2 rounded-lg z-50';
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    </script>

</body>
</html>
