# 小铁台球商城原型项目

## 项目概述

这是一个专为台球爱好者设计的移动端商城应用原型，集成了商品购买、课程预约、维修服务、回收服务等多种功能。项目采用现代化的移动端设计，提供流畅的用户体验。

## 技术栈

- **前端框架**: HTML5 + CSS3 + JavaScript
- **UI框架**: Tailwind CSS
- **图标库**: Font Awesome 6.5.1
- **设计风格**: 移动端优先，暗色主题
- **响应式**: 适配iOS/Android移动设备

## 项目结构

```
billiards_mall_prototype/
├── README.md                    # 项目说明文档
├── home_rich.html              # 首页（丰富版）
├── mall_list_enhanced.html     # 商品列表页
├── product_detail.html         # 商品详情页
├── search.html                 # 搜索页面
├── cart.html                   # 购物车页面
├── coach_list.html             # 教练列表页
├── coach_detail.html           # 教练详情页
├── booking_confirm.html        # 预约确认页
├── booking_success.html        # 预约成功页
├── my_bookings.html            # 我的预约页
├── repair_service.html         # 维修服务页
├── repair_form.html            # 维修申请表单
├── repair_progress.html        # 维修进度查询
├── recycle_service.html        # 回收服务页
├── profile_enhanced.html       # 个人中心（增强版）
├── membership_center.html      # 会员中心
└── distribution_center.html    # 分销中心
```

## 核心功能模块

### 1. 商城模块
- **首页丰富化**: 轮播图、快捷入口、商品推荐、活动展示
- **商品列表**: 分类筛选、排序、搜索功能
- **商品详情**: 图片轮播、规格选择、分享功能
- **购物车**: 商品管理、数量调整、结算功能
- **搜索功能**: 智能搜索建议、历史记录、热门推荐

### 2. 课程预约模块
- **教练列表**: 教练信息展示、筛选功能
- **教练详情**: 详细介绍、时间选择、预约功能
- **预约确认**: 信息填写、支付确认
- **预约管理**: 预约状态跟踪、改期取消

### 3. 维修/回收进度模块
- **维修服务**: 服务类型、流程说明、申请入口
- **维修申请**: 详细表单、图片上传、服务选项
- **进度查询**: 时间线设计、实时状态更新
- **回收服务**: 价格参考、环保理念、预约回收

### 4. 个人中心模块
- **用户信息**: 头像、等级、积分展示
- **会员中心**: 等级体系、特权展示、积分活动
- **分销中心**: 佣金规则、推广工具、收益统计
- **订单管理**: 多类型订单统一管理

## 设计特色

### 视觉设计
- **暗色主题**: 符合台球运动的专业氛围
- **黄色主色调**: 突出重要信息和操作按钮
- **渐变效果**: 增强视觉层次和现代感
- **圆角设计**: 提升界面友好度

### 交互设计
- **iOS风格**: 状态栏、导航栏设计
- **手势支持**: 轮播图滑动、下拉刷新
- **反馈机制**: 加载状态、成功提示
- **无障碍设计**: 清晰的视觉层次和操作反馈

### 用户体验
- **快速导航**: 底部Tab栏、面包屑导航
- **智能推荐**: 基于用户行为的个性化推荐
- **一键操作**: 简化复杂流程，提高操作效率
- **信息透明**: 详细的进度展示和状态说明

## 核心页面说明

### 首页 (home_rich.html)
- 轮播图展示热门活动
- 快捷入口：商城、预约、维修、回收
- 商品推荐和分类展示
- 会员权益和积分系统

### 商品列表 (mall_list_enhanced.html)
- 分类Tab切换
- 筛选和排序功能
- 商品卡片展示
- 无限滚动加载

### 商品详情 (product_detail.html)
- 图片轮播展示
- 规格选择弹窗
- 分享功能
- 购买和加购物车

### 教练详情 (coach_detail.html)
- 教练信息展示
- 日历时间选择
- 预约确认流程
- 评价和收藏功能

### 维修进度 (repair_progress.html)
- 时间线设计
- 实时状态更新
- 联系方式展示
- 进度查询功能

### 个人中心 (profile_enhanced.html)
- 用户信息卡片
- 快速统计数据
- 功能模块分组
- 会员等级展示

## 技术实现要点

### 响应式设计
```css
/* 移动端优先 */
.container {
    width: 100%;
    max-width: 428px;
    margin: 0 auto;
}

/* 适配不同屏幕尺寸 */
@media (min-width: 768px) {
    .container {
        max-width: 768px;
    }
}
```

### 交互动画
```css
/* 平滑过渡效果 */
.transition-all {
    transition: all 0.3s ease-in-out;
}

/* 悬停效果 */
.hover-effect:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}
```

### JavaScript功能
```javascript
// 轮播图自动播放
function autoSlide() {
    setInterval(() => {
        currentSlide = (currentSlide + 1) % totalSlides;
        updateSlider();
    }, 3000);
}

// 购物车数量更新
function updateCartCount() {
    const count = getCartItemCount();
    document.getElementById('cartCount').textContent = count;
}
```

## 部署说明

1. **本地预览**
   ```bash
   # 使用任意HTTP服务器
   python -m http.server 8000
   # 或使用Node.js
   npx serve .
   ```

2. **访问地址**
   ```
   http://localhost:8000/home_rich.html
   ```

3. **移动端调试**
   - 使用Chrome DevTools的设备模拟器
   - 推荐使用iPhone 14 Pro (393x852) 进行预览

## 后续优化建议

### 性能优化
- 图片懒加载和压缩
- CSS/JS文件压缩和合并
- 使用CDN加速静态资源
- 实现PWA离线缓存

### 功能扩展
- 用户登录注册系统
- 支付接口集成
- 消息推送功能
- 数据统计分析

### 技术升级
- 迁移到Vue.js/React框架
- 使用TypeScript增强类型安全
- 集成状态管理工具
- 添加单元测试

## 联系信息

如有问题或建议，请联系开发团队。

---

**版本**: v1.0.0  
**更新时间**: 2024年1月15日  
**开发者**: Augment Agent
