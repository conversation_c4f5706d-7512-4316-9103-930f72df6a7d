<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>我的预约</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #1A1A1A; color: white; font-family: sans-serif; }
        .booking-card {
            background: linear-gradient(135deg, #2D3748 0%, #1A202C 100%);
            border: 1px solid #4A5568;
        }
        .status-upcoming { color: #F6E05E; background-color: rgba(246, 224, 94, 0.1); }
        .status-completed { color: #68D391; background-color: rgba(104, 211, 145, 0.1); }
        .status-cancelled { color: #FC8181; background-color: rgba(252, 129, 129, 0.1); }
    </style>
</head>
<body class="pb-24">

    <!-- iOS Status Bar -->
    <div class="fixed top-0 left-0 right-0 h-11 bg-black/30 backdrop-blur-sm z-50 px-4 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
        <div class="flex items-center gap-2"><i class="fa-solid fa-signal"></i><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
    </div>
    
    <!-- Header -->
    <div class="fixed top-11 left-0 right-0 bg-gray-900/90 backdrop-blur-md z-40 px-4 py-3 border-b border-gray-700">
        <div class="flex items-center gap-3">
            <button onclick="history.back()" class="text-white"><i class="fa-solid fa-arrow-left text-xl"></i></button>
            <h1 class="text-lg font-bold flex-1">我的预约</h1>
            <button onclick="bookNewClass()" class="text-yellow-400"><i class="fa-solid fa-plus text-xl"></i></button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="pt-24">
        <!-- Status Tabs -->
        <div class="bg-gray-800 px-4 py-3 sticky top-24 z-30">
            <div class="flex gap-4 overflow-x-auto">
                <button class="status-tab bg-yellow-400 text-black px-4 py-2 rounded-full text-sm font-bold whitespace-nowrap" data-status="all">全部</button>
                <button class="status-tab bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap" data-status="upcoming">待上课</button>
                <button class="status-tab bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap" data-status="completed">已完成</button>
                <button class="status-tab bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap" data-status="cancelled">已取消</button>
            </div>
        </div>

        <!-- Booking List -->
        <div class="p-4 space-y-4" id="bookingList">
            <!-- Upcoming Booking -->
            <div class="booking-card rounded-2xl p-4">
                <div class="flex items-start gap-4">
                    <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=80&auto=format&fit=crop" class="w-16 h-16 rounded-full object-cover border-2 border-yellow-400" alt="Coach Avatar">
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <div>
                                <h3 class="text-lg font-bold">张教练</h3>
                                <div class="status-upcoming inline-block px-2 py-1 rounded-full text-xs font-bold mt-1">
                                    待上课
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-yellow-400 font-bold">¥160</p>
                                <p class="text-xs text-gray-400">已支付</p>
                            </div>
                        </div>
                        
                        <div class="space-y-2 mb-3">
                            <div class="flex items-center gap-2 text-sm">
                                <i class="fa-solid fa-calendar text-gray-400 w-4"></i>
                                <span class="text-gray-300">2024年1月15日 09:00-10:00</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <i class="fa-solid fa-location-dot text-gray-400 w-4"></i>
                                <span class="text-gray-300">思明店</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <i class="fa-solid fa-user text-gray-400 w-4"></i>
                                <span class="text-gray-300">一对一指导</span>
                            </div>
                        </div>
                        
                        <div class="flex gap-2">
                            <button onclick="contactCoach(1)" class="flex-1 bg-gray-700 text-white py-2 px-3 rounded-lg text-sm font-bold">
                                联系教练
                            </button>
                            <button onclick="reschedule(1)" class="flex-1 bg-yellow-400 text-black py-2 px-3 rounded-lg text-sm font-bold">
                                改期
                            </button>
                            <button onclick="cancelBooking(1)" class="bg-gray-600 text-gray-300 py-2 px-3 rounded-lg text-sm">
                                取消
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Completed Booking -->
            <div class="booking-card rounded-2xl p-4">
                <div class="flex items-start gap-4">
                    <img src="https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?q=80&w=80&auto=format&fit=crop" class="w-16 h-16 rounded-full object-cover border-2 border-green-400" alt="Coach Avatar">
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <div>
                                <h3 class="text-lg font-bold">李教练</h3>
                                <div class="status-completed inline-block px-2 py-1 rounded-full text-xs font-bold mt-1">
                                    已完成
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-green-400 font-bold">¥150</p>
                                <p class="text-xs text-gray-400">已支付</p>
                            </div>
                        </div>
                        
                        <div class="space-y-2 mb-3">
                            <div class="flex items-center gap-2 text-sm">
                                <i class="fa-solid fa-calendar text-gray-400 w-4"></i>
                                <span class="text-gray-300">2024年1月10日 14:00-15:00</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <i class="fa-solid fa-location-dot text-gray-400 w-4"></i>
                                <span class="text-gray-300">湖里店</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <i class="fa-solid fa-star text-yellow-400 w-4"></i>
                                <span class="text-gray-300">已评价 5.0分</span>
                            </div>
                        </div>
                        
                        <div class="flex gap-2">
                            <button onclick="viewDetails(2)" class="flex-1 bg-gray-700 text-white py-2 px-3 rounded-lg text-sm font-bold">
                                查看详情
                            </button>
                            <button onclick="bookAgainWith(2)" class="flex-1 bg-yellow-400 text-black py-2 px-3 rounded-lg text-sm font-bold">
                                再次预约
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Cancelled Booking -->
            <div class="booking-card rounded-2xl p-4 opacity-75">
                <div class="flex items-start gap-4">
                    <img src="https://images.unsplash.com/photo-1500648767791-00dcc994a43e?q=80&w=80&auto=format&fit=crop" class="w-16 h-16 rounded-full object-cover border-2 border-gray-500" alt="Coach Avatar">
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <div>
                                <h3 class="text-lg font-bold">王教练</h3>
                                <div class="status-cancelled inline-block px-2 py-1 rounded-full text-xs font-bold mt-1">
                                    已取消
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-gray-400 font-bold">¥280</p>
                                <p class="text-xs text-gray-400">已退款</p>
                            </div>
                        </div>
                        
                        <div class="space-y-2 mb-3">
                            <div class="flex items-center gap-2 text-sm">
                                <i class="fa-solid fa-calendar text-gray-400 w-4"></i>
                                <span class="text-gray-300">2024年1月08日 16:00-17:00</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <i class="fa-solid fa-location-dot text-gray-400 w-4"></i>
                                <span class="text-gray-300">集美店</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <i class="fa-solid fa-info-circle text-gray-400 w-4"></i>
                                <span class="text-gray-300">教练临时有事取消</span>
                            </div>
                        </div>
                        
                        <div class="flex gap-2">
                            <button onclick="viewDetails(3)" class="flex-1 bg-gray-700 text-white py-2 px-3 rounded-lg text-sm font-bold">
                                查看详情
                            </button>
                            <button onclick="bookAgainWith(3)" class="flex-1 bg-gray-600 text-gray-300 py-2 px-3 rounded-lg text-sm">
                                重新预约
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Another Completed Booking -->
            <div class="booking-card rounded-2xl p-4">
                <div class="flex items-start gap-4">
                    <img src="https://images.unsplash.com/photo-1519085360753-af0119f7cbe7?q=80&w=80&auto=format&fit=crop" class="w-16 h-16 rounded-full object-cover border-2 border-green-400" alt="Coach Avatar">
                    <div class="flex-1">
                        <div class="flex items-start justify-between mb-2">
                            <div>
                                <h3 class="text-lg font-bold">陈教练</h3>
                                <div class="status-completed inline-block px-2 py-1 rounded-full text-xs font-bold mt-1">
                                    已完成
                                </div>
                            </div>
                            <div class="text-right">
                                <p class="text-green-400 font-bold">¥200</p>
                                <p class="text-xs text-gray-400">已支付</p>
                            </div>
                        </div>
                        
                        <div class="space-y-2 mb-3">
                            <div class="flex items-center gap-2 text-sm">
                                <i class="fa-solid fa-calendar text-gray-400 w-4"></i>
                                <span class="text-gray-300">2024年1月05日 19:00-20:00</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <i class="fa-solid fa-location-dot text-gray-400 w-4"></i>
                                <span class="text-gray-300">海沧店</span>
                            </div>
                            <div class="flex items-center gap-2 text-sm">
                                <i class="fa-solid fa-star text-yellow-400 w-4"></i>
                                <span class="text-gray-300">已评价 4.5分</span>
                            </div>
                        </div>
                        
                        <div class="flex gap-2">
                            <button onclick="viewDetails(4)" class="flex-1 bg-gray-700 text-white py-2 px-3 rounded-lg text-sm font-bold">
                                查看详情
                            </button>
                            <button onclick="bookAgainWith(4)" class="flex-1 bg-yellow-400 text-black py-2 px-3 rounded-lg text-sm font-bold">
                                再次预约
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Empty State (Hidden by default) -->
        <div class="hidden text-center py-20" id="emptyState">
            <i class="fa-solid fa-calendar-xmark text-6xl text-gray-600 mb-4"></i>
            <p class="text-gray-400 text-lg mb-4">暂无预约记录</p>
            <button onclick="bookNewClass()" class="bg-yellow-400 text-black px-8 py-3 rounded-lg font-bold">
                立即预约
            </button>
        </div>
    </div>

    <!-- Bottom Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 h-20 bg-gray-900/80 backdrop-blur-md flex justify-around items-center text-gray-400 border-t border-gray-700">
        <a href="home_rich.html" class="flex flex-col items-center gap-1"><i class="fa-solid fa-house text-xl"></i><span class="text-xs">首页</span></a>
        <a href="mall_list_enhanced.html" class="flex flex-col items-center gap-1"><i class="fa-solid fa-store text-xl"></i><span class="text-xs">商城</span></a>
        <a href="#" class="w-16 h-16 -mt-8 bg-yellow-400 rounded-full flex items-center justify-center text-black shadow-lg"><i class="fa-solid fa-qrcode text-3xl"></i></a>
        <a href="#" class="flex flex-col items-center gap-1 text-yellow-400"><i class="fa-solid fa-file-invoice text-xl"></i><span class="text-xs font-bold">订单</span></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-user text-xl"></i><span class="text-xs">我的</span></a>
    </div>

    <script>
        let currentStatus = 'all';

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            setupStatusTabs();
        });

        function setupStatusTabs() {
            document.querySelectorAll('.status-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    const status = this.dataset.status;
                    switchStatus(status);
                });
            });
        }

        function switchStatus(status) {
            currentStatus = status;
            
            // 更新Tab样式
            document.querySelectorAll('.status-tab').forEach(tab => {
                if (tab.dataset.status === status) {
                    tab.className = 'status-tab bg-yellow-400 text-black px-4 py-2 rounded-full text-sm font-bold whitespace-nowrap';
                } else {
                    tab.className = 'status-tab bg-gray-700 text-gray-300 px-4 py-2 rounded-full text-sm whitespace-nowrap';
                }
            });
            
            filterBookings();
        }

        function filterBookings() {
            const bookingCards = document.querySelectorAll('.booking-card');
            let visibleCount = 0;
            
            bookingCards.forEach(card => {
                const statusElement = card.querySelector('[class*="status-"]');
                const cardStatus = statusElement.textContent.trim();
                
                let shouldShow = false;
                switch (currentStatus) {
                    case 'all':
                        shouldShow = true;
                        break;
                    case 'upcoming':
                        shouldShow = cardStatus === '待上课';
                        break;
                    case 'completed':
                        shouldShow = cardStatus === '已完成';
                        break;
                    case 'cancelled':
                        shouldShow = cardStatus === '已取消';
                        break;
                }
                
                if (shouldShow) {
                    card.style.display = 'block';
                    visibleCount++;
                } else {
                    card.style.display = 'none';
                }
            });
            
            // 显示/隐藏空状态
            const emptyState = document.getElementById('emptyState');
            const bookingList = document.getElementById('bookingList');
            
            if (visibleCount === 0) {
                bookingList.style.display = 'none';
                emptyState.classList.remove('hidden');
            } else {
                bookingList.style.display = 'block';
                emptyState.classList.add('hidden');
            }
        }

        function contactCoach(bookingId) {
            alert(`联系教练 - 预约ID: ${bookingId}`);
        }

        function reschedule(bookingId) {
            if (confirm('确定要改期这次预约吗？')) {
                alert(`改期预约 - 预约ID: ${bookingId}`);
            }
        }

        function cancelBooking(bookingId) {
            if (confirm('确定要取消这次预约吗？取消后费用将原路退回。')) {
                alert(`取消预约 - 预约ID: ${bookingId}`);
            }
        }

        function viewDetails(bookingId) {
            window.location.href = `booking_detail.html?id=${bookingId}`;
        }

        function bookAgainWith(coachId) {
            window.location.href = `coach_detail.html?id=${coachId}`;
        }

        function bookNewClass() {
            window.location.href = 'coach_list.html';
        }
    </script>

</body>
</html>
