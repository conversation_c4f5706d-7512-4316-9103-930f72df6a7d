<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商城列表</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #1A1A1A; color: white; font-family: sans-serif; }
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .filter-panel {
            transform: translateX(100%);
            transition: transform 0.3s ease-in-out;
        }
        .filter-panel.active {
            transform: translateX(0);
        }
    </style>
</head>
<body class="pb-24">

    <!-- iOS Status Bar -->
    <div class="fixed top-0 left-0 right-0 h-11 bg-black/30 backdrop-blur-sm z-50 px-4 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
        <div class="flex items-center gap-2"><i class="fa-solid fa-signal"></i><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
    </div>
    
    <!-- Header -->
    <div class="fixed top-11 left-0 right-0 bg-gray-900/90 backdrop-blur-md z-40 px-4 py-3 border-b border-gray-700">
        <div class="flex items-center gap-3">
            <button onclick="history.back()" class="text-white"><i class="fa-solid fa-arrow-left text-xl"></i></button>
            <h1 class="text-lg font-bold flex-1">小铁商城</h1>
            <button onclick="openSearch()" class="text-white"><i class="fa-solid fa-search text-xl"></i></button>
            <button onclick="openCart()" class="text-white relative">
                <i class="fa-solid fa-shopping-cart text-xl"></i>
                <span class="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center" id="cartCount">2</span>
            </button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="pt-24">
        <!-- Category Tabs -->
        <div class="bg-gray-800 px-4 py-3 sticky top-24 z-30">
            <div class="flex gap-6 overflow-x-auto" id="categoryTabs">
                <button class="category-tab text-yellow-400 font-bold border-b-2 border-yellow-400 pb-1 whitespace-nowrap" data-category="all">全部</button>
                <button class="category-tab text-gray-400 whitespace-nowrap" data-category="cues">台球杆</button>
                <button class="category-tab text-gray-400 whitespace-nowrap" data-category="accessories">配件</button>
                <button class="category-tab text-gray-400 whitespace-nowrap" data-category="tables">球桌</button>
                <button class="category-tab text-gray-400 whitespace-nowrap" data-category="peripherals">周边</button>
            </div>
        </div>

        <!-- Filter Bar -->
        <div class="bg-gray-800 px-4 py-3 border-t border-gray-700 flex justify-between items-center sticky top-36 z-30">
            <div class="flex gap-4">
                <button class="sort-btn text-yellow-400 font-bold" data-sort="comprehensive">综合</button>
                <button class="sort-btn text-gray-400" data-sort="sales">销量</button>
                <button class="sort-btn text-gray-400 flex items-center gap-1" data-sort="price">
                    价格 <i class="fa-solid fa-sort text-xs" id="priceIcon"></i>
                </button>
            </div>
            <button onclick="toggleFilterPanel()" class="text-gray-400 flex items-center gap-1">
                <i class="fa-solid fa-filter"></i> 筛选
            </button>
        </div>

        <!-- Product Grid -->
        <div class="p-4" id="productContainer">
            <div class="grid grid-cols-2 gap-4" id="productGrid">
                <!-- Products will be dynamically loaded here -->
            </div>
            
            <!-- Loading indicator -->
            <div class="text-center py-8 hidden" id="loadingIndicator">
                <i class="fa-solid fa-spinner fa-spin text-2xl text-gray-400"></i>
                <p class="text-gray-400 mt-2">加载中...</p>
            </div>
        </div>
    </div>

    <!-- Filter Panel (右侧滑出) -->
    <div class="fixed inset-0 bg-black/50 z-50 hidden" id="filterOverlay" onclick="closeFilterPanel()">
        <div class="filter-panel fixed right-0 top-0 bottom-0 w-80 bg-gray-900 p-6" onclick="event.stopPropagation()">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-lg font-bold">筛选条件</h3>
                <button onclick="closeFilterPanel()" class="text-gray-400">
                    <i class="fa-solid fa-times text-xl"></i>
                </button>
            </div>
            
            <!-- 品牌筛选 -->
            <div class="mb-6">
                <h4 class="font-bold mb-3">品牌</h4>
                <div class="space-y-2">
                    <label class="flex items-center gap-2">
                        <input type="checkbox" class="brand-filter" value="cuppa" />
                        <span>Cuppa</span>
                    </label>
                    <label class="flex items-center gap-2">
                        <input type="checkbox" class="brand-filter" value="predator" />
                        <span>Predator</span>
                    </label>
                    <label class="flex items-center gap-2">
                        <input type="checkbox" class="brand-filter" value="mcdermott" />
                        <span>McDermott</span>
                    </label>
                </div>
            </div>
            
            <!-- 价格区间 -->
            <div class="mb-6">
                <h4 class="font-bold mb-3">价格区间</h4>
                <div class="space-y-2">
                    <label class="flex items-center gap-2">
                        <input type="radio" name="priceRange" class="price-filter" value="0-100" />
                        <span>¥0 - ¥100</span>
                    </label>
                    <label class="flex items-center gap-2">
                        <input type="radio" name="priceRange" class="price-filter" value="100-500" />
                        <span>¥100 - ¥500</span>
                    </label>
                    <label class="flex items-center gap-2">
                        <input type="radio" name="priceRange" class="price-filter" value="500-1000" />
                        <span>¥500 - ¥1000</span>
                    </label>
                    <label class="flex items-center gap-2">
                        <input type="radio" name="priceRange" class="price-filter" value="1000+" />
                        <span>¥1000以上</span>
                    </label>
                </div>
            </div>
            
            <!-- 按钮 -->
            <div class="flex gap-3 mt-8">
                <button onclick="resetFilters()" class="flex-1 py-3 border border-gray-600 rounded-lg text-gray-400">
                    重置
                </button>
                <button onclick="applyFilters()" class="flex-1 py-3 bg-yellow-400 text-black rounded-lg font-bold">
                    确定
                </button>
            </div>
        </div>
    </div>

    <!-- Bottom Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 h-20 bg-gray-900/80 backdrop-blur-md flex justify-around items-center text-gray-400 border-t border-gray-700">
        <a href="home_rich.html" class="flex flex-col items-center gap-1"><i class="fa-solid fa-house text-xl"></i><span class="text-xs">首页</span></a>
        <a href="#" class="flex flex-col items-center gap-1 text-yellow-400"><i class="fa-solid fa-store text-xl"></i><span class="text-xs font-bold">商城</span></a>
        <a href="#" class="w-16 h-16 -mt-8 bg-yellow-400 rounded-full flex items-center justify-center text-black shadow-lg"><i class="fa-solid fa-qrcode text-3xl"></i></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-file-invoice text-xl"></i><span class="text-xs">订单</span></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-user text-xl"></i><span class="text-xs">我的</span></a>
    </div>

    <script>
        // 模拟商品数据
        const products = [
            {
                id: 1,
                name: "Cuppa-JR 职业级枫木球杆",
                price: 1280,
                image: "https://images.unsplash.com/photo-1557996739-b883c79d1297?q=80&w=600&auto=format&fit=crop",
                category: "cues",
                brand: "cuppa",
                sales: 128,
                rating: 5
            },
            {
                id: 2,
                name: "进口比利时水晶球 标准版",
                price: 899,
                image: "https://images.unsplash.com/photo-1631181827493-276949033331?q=80&w=600&auto=format&fit=crop",
                category: "accessories",
                brand: "predator",
                sales: 89,
                rating: 4
            },
            {
                id: 3,
                name: "专业防滑台球手套",
                price: 49,
                image: "https://plus.unsplash.com/premium_photo-1661662991873-6784a0c897f2?q=80&w=600&auto=format&fit=crop",
                category: "accessories",
                brand: "mcdermott",
                sales: 256,
                rating: 5
            },
            {
                id: 4,
                name: "大师级巧克粉 防滑专用",
                price: 128,
                image: "https://images.unsplash.com/photo-1594759881342-a059d18e5898?q=80&w=600&auto=format&fit=crop",
                category: "accessories",
                brand: "cuppa",
                sales: 67,
                rating: 4
            },
            {
                id: 5,
                name: "专业台球桌 家用标准版",
                price: 3999,
                image: "https://images.unsplash.com/photo-1559523161-0d5b3d99d32b?q=80&w=600&auto=format&fit=crop",
                category: "tables",
                brand: "predator",
                sales: 23,
                rating: 5
            },
            {
                id: 6,
                name: "碳纤维球杆头 专业级",
                price: 299,
                image: "https://images.unsplash.com/photo-1574116035011-e68651815f43?q=80&w=600&auto=format&fit=crop",
                category: "cues",
                brand: "mcdermott",
                sales: 156,
                rating: 4
            }
        ];

        let currentCategory = 'all';
        let currentSort = 'comprehensive';
        let priceOrder = 'asc'; // asc or desc
        let currentFilters = {
            brands: [],
            priceRange: null
        };

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            loadProducts();
            setupEventListeners();
        });

        function setupEventListeners() {
            // 分类Tab点击事件
            document.querySelectorAll('.category-tab').forEach(tab => {
                tab.addEventListener('click', function() {
                    const category = this.dataset.category;
                    switchCategory(category);
                });
            });

            // 排序按钮点击事件
            document.querySelectorAll('.sort-btn').forEach(btn => {
                btn.addEventListener('click', function() {
                    const sort = this.dataset.sort;
                    switchSort(sort);
                });
            });
        }

        function switchCategory(category) {
            currentCategory = category;
            
            // 更新Tab样式
            document.querySelectorAll('.category-tab').forEach(tab => {
                if (tab.dataset.category === category) {
                    tab.className = 'category-tab text-yellow-400 font-bold border-b-2 border-yellow-400 pb-1 whitespace-nowrap';
                } else {
                    tab.className = 'category-tab text-gray-400 whitespace-nowrap';
                }
            });
            
            loadProducts();
        }

        function switchSort(sort) {
            if (sort === 'price') {
                priceOrder = priceOrder === 'asc' ? 'desc' : 'asc';
                const icon = document.getElementById('priceIcon');
                icon.className = priceOrder === 'asc' ? 'fa-solid fa-sort-up text-xs' : 'fa-solid fa-sort-down text-xs';
            }
            
            currentSort = sort;
            
            // 更新排序按钮样式
            document.querySelectorAll('.sort-btn').forEach(btn => {
                if (btn.dataset.sort === sort) {
                    btn.className = btn.className.replace('text-gray-400', 'text-yellow-400 font-bold');
                } else {
                    btn.className = btn.className.replace('text-yellow-400 font-bold', 'text-gray-400');
                }
            });
            
            loadProducts();
        }

        function loadProducts() {
            const grid = document.getElementById('productGrid');
            let filteredProducts = [...products];
            
            // 分类筛选
            if (currentCategory !== 'all') {
                filteredProducts = filteredProducts.filter(p => p.category === currentCategory);
            }
            
            // 品牌筛选
            if (currentFilters.brands.length > 0) {
                filteredProducts = filteredProducts.filter(p => currentFilters.brands.includes(p.brand));
            }
            
            // 价格区间筛选
            if (currentFilters.priceRange) {
                const [min, max] = currentFilters.priceRange.split('-');
                if (max === '+') {
                    filteredProducts = filteredProducts.filter(p => p.price >= parseInt(min));
                } else {
                    filteredProducts = filteredProducts.filter(p => p.price >= parseInt(min) && p.price <= parseInt(max));
                }
            }
            
            // 排序
            switch (currentSort) {
                case 'sales':
                    filteredProducts.sort((a, b) => b.sales - a.sales);
                    break;
                case 'price':
                    if (priceOrder === 'asc') {
                        filteredProducts.sort((a, b) => a.price - b.price);
                    } else {
                        filteredProducts.sort((a, b) => b.price - a.price);
                    }
                    break;
                default: // comprehensive
                    filteredProducts.sort((a, b) => b.rating * b.sales - a.rating * a.sales);
            }
            
            // 渲染商品
            grid.innerHTML = filteredProducts.map(product => `
                <div class="bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 cursor-pointer" onclick="openProductDetail(${product.id})">
                    <img src="${product.image}" class="w-full h-40 object-cover" alt="${product.name}">
                    <div class="p-3">
                        <p class="text-sm font-semibold line-clamp-2">${product.name}</p>
                        <p class="text-lg font-bold text-yellow-400 mt-2">¥ <span class="text-xl">${product.price}</span></p>
                        <div class="flex items-center justify-between mt-2">
                            <div class="flex text-yellow-400 text-xs">
                                ${Array(5).fill().map((_, i) => 
                                    i < product.rating ? '<i class="fa-solid fa-star"></i>' : '<i class="fa-regular fa-star"></i>'
                                ).join('')}
                            </div>
                            <span class="text-xs text-gray-400">已售${product.sales}</span>
                        </div>
                    </div>
                </div>
            `).join('');
        }

        // 筛选面板相关函数
        function toggleFilterPanel() {
            const overlay = document.getElementById('filterOverlay');
            const panel = overlay.querySelector('.filter-panel');
            
            overlay.classList.remove('hidden');
            setTimeout(() => panel.classList.add('active'), 10);
        }

        function closeFilterPanel() {
            const overlay = document.getElementById('filterOverlay');
            const panel = overlay.querySelector('.filter-panel');
            
            panel.classList.remove('active');
            setTimeout(() => overlay.classList.add('hidden'), 300);
        }

        function resetFilters() {
            currentFilters = { brands: [], priceRange: null };
            document.querySelectorAll('.brand-filter').forEach(cb => cb.checked = false);
            document.querySelectorAll('.price-filter').forEach(rb => rb.checked = false);
        }

        function applyFilters() {
            // 收集品牌筛选
            currentFilters.brands = Array.from(document.querySelectorAll('.brand-filter:checked')).map(cb => cb.value);
            
            // 收集价格区间筛选
            const priceFilter = document.querySelector('.price-filter:checked');
            currentFilters.priceRange = priceFilter ? priceFilter.value : null;
            
            loadProducts();
            closeFilterPanel();
        }

        // 页面跳转函数
        function openProductDetail(productId) {
            window.location.href = `product_detail.html?id=${productId}`;
        }

        function openSearch() {
            window.location.href = 'search.html';
        }

        function openCart() {
            window.location.href = 'cart.html';
        }
    </script>

</body>
</html>
