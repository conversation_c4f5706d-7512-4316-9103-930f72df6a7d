<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>商品搜索</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #1A1A1A; color: white; font-family: sans-serif; }
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
    </style>
</head>
<body class="pb-24">

    <!-- iOS Status Bar -->
    <div class="fixed top-0 left-0 right-0 h-11 bg-black/30 backdrop-blur-sm z-50 px-4 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
        <div class="flex items-center gap-2"><i class="fa-solid fa-signal"></i><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
    </div>
    
    <!-- Search Header -->
    <div class="fixed top-11 left-0 right-0 bg-gray-900/90 backdrop-blur-md z-40 px-4 py-3 border-b border-gray-700">
        <div class="flex items-center gap-3">
            <button onclick="history.back()" class="text-white"><i class="fa-solid fa-arrow-left text-xl"></i></button>
            <div class="flex-1 bg-gray-700 rounded-full flex items-center px-4 gap-2">
                <i class="fa-solid fa-search text-gray-400"></i>
                <input type="text" id="searchInput" placeholder="搜索球杆、配件" class="w-full bg-transparent text-white placeholder-gray-400 outline-none py-2" autocomplete="off">
                <button onclick="clearSearch()" class="text-gray-400 hidden" id="clearBtn">
                    <i class="fa-solid fa-times"></i>
                </button>
            </div>
            <button onclick="performSearch()" class="text-yellow-400 font-bold">搜索</button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="pt-24">
        <!-- Search Suggestions (显示在输入时) -->
        <div class="hidden" id="searchSuggestions">
            <div class="px-4 py-2 border-b border-gray-700">
                <h3 class="text-sm font-bold text-gray-400">搜索建议</h3>
            </div>
            <div id="suggestionsList">
                <!-- 动态生成搜索建议 -->
            </div>
        </div>

        <!-- Default State (历史搜索 + 热门推荐) -->
        <div id="defaultState">
            <!-- Search History -->
            <div class="px-4 py-4" id="searchHistorySection">
                <div class="flex items-center justify-between mb-3">
                    <h3 class="font-bold">历史搜索</h3>
                    <button onclick="clearHistory()" class="text-gray-400 text-sm">
                        <i class="fa-solid fa-trash"></i> 清空
                    </button>
                </div>
                <div class="flex flex-wrap gap-2" id="historyTags">
                    <span class="bg-gray-700 px-3 py-1 rounded-full text-sm cursor-pointer" onclick="searchKeyword('台球杆')">台球杆</span>
                    <span class="bg-gray-700 px-3 py-1 rounded-full text-sm cursor-pointer" onclick="searchKeyword('水晶球')">水晶球</span>
                    <span class="bg-gray-700 px-3 py-1 rounded-full text-sm cursor-pointer" onclick="searchKeyword('手套')">手套</span>
                </div>
            </div>

            <!-- Hot Keywords -->
            <div class="px-4 py-4 border-t border-gray-700">
                <h3 class="font-bold mb-3">热门推荐</h3>
                <div class="flex flex-wrap gap-2">
                    <span class="bg-gradient-to-r from-yellow-400 to-orange-500 text-black px-3 py-1 rounded-full text-sm font-bold cursor-pointer" onclick="searchKeyword('Cuppa球杆')">
                        <i class="fa-solid fa-fire"></i> Cuppa球杆
                    </span>
                    <span class="bg-gray-700 px-3 py-1 rounded-full text-sm cursor-pointer" onclick="searchKeyword('专业球桌')">专业球桌</span>
                    <span class="bg-gray-700 px-3 py-1 rounded-full text-sm cursor-pointer" onclick="searchKeyword('巧克粉')">巧克粉</span>
                    <span class="bg-gray-700 px-3 py-1 rounded-full text-sm cursor-pointer" onclick="searchKeyword('碳纤维')">碳纤维</span>
                    <span class="bg-gray-700 px-3 py-1 rounded-full text-sm cursor-pointer" onclick="searchKeyword('防滑手套')">防滑手套</span>
                    <span class="bg-gray-700 px-3 py-1 rounded-full text-sm cursor-pointer" onclick="searchKeyword('比利时球')">比利时球</span>
                </div>
            </div>
        </div>

        <!-- Search Results -->
        <div class="hidden" id="searchResults">
            <div class="px-4 py-3 border-b border-gray-700">
                <p class="text-sm text-gray-400">
                    为您找到 <span class="text-yellow-400" id="resultCount">0</span> 个相关商品
                </p>
            </div>
            <div class="p-4">
                <div class="grid grid-cols-2 gap-4" id="resultsGrid">
                    <!-- 搜索结果将在这里动态生成 -->
                </div>
            </div>
        </div>

        <!-- No Results -->
        <div class="hidden text-center py-20" id="noResults">
            <i class="fa-solid fa-search text-6xl text-gray-600 mb-4"></i>
            <p class="text-gray-400 text-lg mb-2">没有找到相关商品</p>
            <p class="text-gray-500 text-sm mb-6">试试其他关键词或浏览推荐商品</p>
            <button onclick="showRecommendations()" class="bg-yellow-400 text-black px-6 py-2 rounded-lg font-bold">
                查看推荐
            </button>
        </div>
    </div>

    <!-- Bottom Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 h-20 bg-gray-900/80 backdrop-blur-md flex justify-around items-center text-gray-400 border-t border-gray-700">
        <a href="home_rich.html" class="flex flex-col items-center gap-1"><i class="fa-solid fa-house text-xl"></i><span class="text-xs">首页</span></a>
        <a href="mall_list_enhanced.html" class="flex flex-col items-center gap-1 text-yellow-400"><i class="fa-solid fa-store text-xl"></i><span class="text-xs font-bold">商城</span></a>
        <a href="#" class="w-16 h-16 -mt-8 bg-yellow-400 rounded-full flex items-center justify-center text-black shadow-lg"><i class="fa-solid fa-qrcode text-3xl"></i></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-file-invoice text-xl"></i><span class="text-xs">订单</span></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-user text-xl"></i><span class="text-xs">我的</span></a>
    </div>

    <script>
        // 模拟商品数据
        const allProducts = [
            {
                id: 1,
                name: "Cuppa-JR 职业级枫木球杆",
                price: 1280,
                image: "https://images.unsplash.com/photo-1557996739-b883c79d1297?q=80&w=600&auto=format&fit=crop",
                keywords: ["cuppa", "球杆", "枫木", "职业级"]
            },
            {
                id: 2,
                name: "进口比利时水晶球 标准版",
                price: 899,
                image: "https://images.unsplash.com/photo-1631181827493-276949033331?q=80&w=600&auto=format&fit=crop",
                keywords: ["水晶球", "比利时", "进口", "标准版"]
            },
            {
                id: 3,
                name: "专业防滑台球手套",
                price: 49,
                image: "https://plus.unsplash.com/premium_photo-1661662991873-6784a0c897f2?q=80&w=600&auto=format&fit=crop",
                keywords: ["手套", "防滑", "专业", "台球"]
            },
            {
                id: 4,
                name: "大师级巧克粉 防滑专用",
                price: 128,
                image: "https://images.unsplash.com/photo-1594759881342-a059d18e5898?q=80&w=600&auto=format&fit=crop",
                keywords: ["巧克粉", "大师级", "防滑", "专用"]
            },
            {
                id: 5,
                name: "专业台球桌 家用标准版",
                price: 3999,
                image: "https://images.unsplash.com/photo-1559523161-0d5b3d99d32b?q=80&w=600&auto=format&fit=crop",
                keywords: ["台球桌", "专业", "家用", "标准版"]
            },
            {
                id: 6,
                name: "碳纤维球杆头 专业级",
                price: 299,
                image: "https://images.unsplash.com/photo-1574116035011-e68651815f43?q=80&w=600&auto=format&fit=crop",
                keywords: ["碳纤维", "球杆头", "专业级"]
            }
        ];

        let searchHistory = JSON.parse(localStorage.getItem('searchHistory') || '[]');
        let currentKeyword = '';

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            setupSearchInput();
            updateHistoryDisplay();
        });

        function setupSearchInput() {
            const searchInput = document.getElementById('searchInput');
            const clearBtn = document.getElementById('clearBtn');

            searchInput.addEventListener('input', function() {
                const value = this.value.trim();
                
                if (value) {
                    clearBtn.classList.remove('hidden');
                    showSearchSuggestions(value);
                } else {
                    clearBtn.classList.add('hidden');
                    hideSearchSuggestions();
                }
            });

            searchInput.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performSearch();
                }
            });

            // 自动聚焦
            searchInput.focus();
        }

        function showSearchSuggestions(keyword) {
            const suggestions = allProducts.filter(product => 
                product.name.toLowerCase().includes(keyword.toLowerCase()) ||
                product.keywords.some(k => k.toLowerCase().includes(keyword.toLowerCase()))
            );

            const suggestionsList = document.getElementById('suggestionsList');
            suggestionsList.innerHTML = suggestions.slice(0, 5).map(product => `
                <div class="px-4 py-3 border-b border-gray-700 cursor-pointer hover:bg-gray-800" onclick="searchKeyword('${product.name}')">
                    <div class="flex items-center gap-3">
                        <i class="fa-solid fa-search text-gray-400"></i>
                        <span>${product.name}</span>
                    </div>
                </div>
            `).join('');

            document.getElementById('searchSuggestions').classList.remove('hidden');
            document.getElementById('defaultState').classList.add('hidden');
            document.getElementById('searchResults').classList.add('hidden');
            document.getElementById('noResults').classList.add('hidden');
        }

        function hideSearchSuggestions() {
            document.getElementById('searchSuggestions').classList.add('hidden');
            document.getElementById('defaultState').classList.remove('hidden');
        }

        function performSearch() {
            const keyword = document.getElementById('searchInput').value.trim();
            if (!keyword) return;

            searchKeyword(keyword);
        }

        function searchKeyword(keyword) {
            currentKeyword = keyword;
            document.getElementById('searchInput').value = keyword;
            
            // 添加到搜索历史
            addToHistory(keyword);
            
            // 执行搜索
            const results = allProducts.filter(product => 
                product.name.toLowerCase().includes(keyword.toLowerCase()) ||
                product.keywords.some(k => k.toLowerCase().includes(keyword.toLowerCase()))
            );

            displaySearchResults(results);
        }

        function displaySearchResults(results) {
            // 隐藏其他状态
            document.getElementById('defaultState').classList.add('hidden');
            document.getElementById('searchSuggestions').classList.add('hidden');
            
            if (results.length > 0) {
                document.getElementById('resultCount').textContent = results.length;
                document.getElementById('resultsGrid').innerHTML = results.map(product => `
                    <div class="bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 cursor-pointer" onclick="openProductDetail(${product.id})">
                        <img src="${product.image}" class="w-full h-40 object-cover" alt="${product.name}">
                        <div class="p-3">
                            <p class="text-sm font-semibold line-clamp-2">${product.name}</p>
                            <p class="text-lg font-bold text-yellow-400 mt-2">¥ <span class="text-xl">${product.price}</span></p>
                        </div>
                    </div>
                `).join('');
                
                document.getElementById('searchResults').classList.remove('hidden');
                document.getElementById('noResults').classList.add('hidden');
            } else {
                document.getElementById('searchResults').classList.add('hidden');
                document.getElementById('noResults').classList.remove('hidden');
            }
        }

        function addToHistory(keyword) {
            // 移除重复项
            searchHistory = searchHistory.filter(item => item !== keyword);
            // 添加到开头
            searchHistory.unshift(keyword);
            // 限制历史记录数量
            searchHistory = searchHistory.slice(0, 10);
            // 保存到本地存储
            localStorage.setItem('searchHistory', JSON.stringify(searchHistory));
            // 更新显示
            updateHistoryDisplay();
        }

        function updateHistoryDisplay() {
            const historyTags = document.getElementById('historyTags');
            const historySection = document.getElementById('searchHistorySection');
            
            if (searchHistory.length === 0) {
                historySection.classList.add('hidden');
                return;
            }
            
            historySection.classList.remove('hidden');
            historyTags.innerHTML = searchHistory.map(keyword => 
                `<span class="bg-gray-700 px-3 py-1 rounded-full text-sm cursor-pointer" onclick="searchKeyword('${keyword}')">${keyword}</span>`
            ).join('');
        }

        function clearHistory() {
            searchHistory = [];
            localStorage.removeItem('searchHistory');
            updateHistoryDisplay();
        }

        function clearSearch() {
            document.getElementById('searchInput').value = '';
            document.getElementById('clearBtn').classList.add('hidden');
            hideSearchSuggestions();
        }

        function showRecommendations() {
            window.location.href = 'mall_list_enhanced.html';
        }

        function openProductDetail(productId) {
            window.location.href = `product_detail.html?id=${productId}`;
        }
    </script>

</body>
</html>
