<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>教练详情</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #1A1A1A; color: white; font-family: sans-serif; }
        .glow-yellow { box-shadow: 0 0 15px rgba(255, 215, 0, 0.5); }
        .time-slot {
            transition: all 0.3s ease;
        }
        .time-slot.available:hover {
            background-color: #F6E05E;
            color: #1A202C;
        }
        .time-slot.selected {
            background-color: #F6E05E;
            color: #1A202C;
            font-weight: bold;
        }
        .time-slot.unavailable {
            background-color: #4A5568;
            color: #A0AEC0;
            cursor: not-allowed;
        }
        .calendar-day {
            transition: all 0.3s ease;
        }
        .calendar-day.selected {
            background-color: #F6E05E;
            color: #1A202C;
        }
    </style>
</head>
<body class="pb-32">

    <!-- iOS Status Bar -->
    <div class="fixed top-0 left-0 right-0 h-11 bg-black/30 backdrop-blur-sm z-50 px-4 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
        <div class="flex items-center gap-2"><i class="fa-solid fa-signal"></i><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
    </div>
    
    <!-- Header -->
    <div class="fixed top-11 left-0 right-0 bg-gray-900/90 backdrop-blur-md z-40 px-4 py-3 border-b border-gray-700">
        <div class="flex items-center gap-3">
            <button onclick="history.back()" class="text-white"><i class="fa-solid fa-arrow-left text-xl"></i></button>
            <h1 class="text-lg font-bold flex-1">教练详情</h1>
            <button onclick="shareCoach()" class="text-white"><i class="fa-solid fa-share text-xl"></i></button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="pt-24">
        <!-- Coach Profile -->
        <div class="bg-gray-800 p-6">
            <div class="flex gap-4 mb-6">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=120&auto=format&fit=crop" class="w-24 h-24 rounded-full object-cover border-3 border-yellow-400" alt="Coach Avatar">
                <div class="flex-1">
                    <h2 class="text-2xl font-bold mb-2">张教练</h2>
                    <div class="bg-gradient-to-r from-yellow-400 to-orange-500 text-black inline-block px-3 py-1 rounded-full text-sm font-bold mb-3">
                        国家一级教练
                    </div>
                    <div class="flex items-center gap-4 mb-2">
                        <div class="flex items-center gap-1 text-yellow-400">
                            <i class="fa-solid fa-star"></i>
                            <i class="fa-solid fa-star"></i>
                            <i class="fa-solid fa-star"></i>
                            <i class="fa-solid fa-star"></i>
                            <i class="fa-solid fa-star"></i>
                            <span class="text-white text-sm ml-1">5.0</span>
                        </div>
                        <span class="text-gray-400 text-sm">128条评价</span>
                    </div>
                    <p class="text-yellow-400 text-xl font-bold">¥180/小时</p>
                </div>
            </div>

            <!-- Specialties -->
            <div class="mb-6">
                <h3 class="font-bold mb-3">专业领域</h3>
                <div class="flex gap-2">
                    <span class="bg-blue-600 text-white px-3 py-1 rounded-full text-sm">斯诺克</span>
                    <span class="bg-green-600 text-white px-3 py-1 rounded-full text-sm">中式八球</span>
                </div>
            </div>

            <!-- Description -->
            <div class="mb-6">
                <h3 class="font-bold mb-3">教练介绍</h3>
                <p class="text-gray-300 leading-relaxed">
                    10年教学经验，擅长技术分析和心理指导，帮助学员快速提升球技。曾指导多名学员获得省级比赛冠军，
                    教学风格严谨而不失幽默，深受学员喜爱。专注于基础技术训练和比赛心理调节。
                </p>
            </div>

            <!-- Achievements -->
            <div>
                <h3 class="font-bold mb-3">教学成就</h3>
                <div class="grid grid-cols-3 gap-4 text-center">
                    <div class="bg-gray-700 rounded-lg p-3">
                        <p class="text-2xl font-bold text-yellow-400">10+</p>
                        <p class="text-xs text-gray-400">教学年限</p>
                    </div>
                    <div class="bg-gray-700 rounded-lg p-3">
                        <p class="text-2xl font-bold text-yellow-400">500+</p>
                        <p class="text-xs text-gray-400">学员数量</p>
                    </div>
                    <div class="bg-gray-700 rounded-lg p-3">
                        <p class="text-2xl font-bold text-yellow-400">50+</p>
                        <p class="text-xs text-gray-400">获奖学员</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Photo Gallery -->
        <div class="bg-gray-800 mt-2 p-4">
            <h3 class="font-bold mb-3">风采展示</h3>
            <div class="grid grid-cols-3 gap-2">
                <img src="https://images.unsplash.com/photo-1574116035011-e68651815f43?q=80&w=200&auto=format&fit=crop" class="w-full h-20 object-cover rounded-lg" alt="Coach Photo">
                <img src="https://images.unsplash.com/photo-1557996739-b883c79d1297?q=80&w=200&auto=format&fit=crop" class="w-full h-20 object-cover rounded-lg" alt="Coach Photo">
                <img src="https://images.unsplash.com/photo-1559523161-0d5b3d99d32b?q=80&w=200&auto=format&fit=crop" class="w-full h-20 object-cover rounded-lg" alt="Coach Photo">
            </div>
        </div>

        <!-- Booking Calendar -->
        <div class="bg-gray-800 mt-2 p-4">
            <h3 class="font-bold mb-4">选择上课时间</h3>
            
            <!-- Date Selector -->
            <div class="mb-4">
                <h4 class="text-sm font-bold mb-2 text-gray-400">选择日期</h4>
                <div class="flex gap-2 overflow-x-auto pb-2" id="dateSelector">
                    <button class="calendar-day selected flex-shrink-0 bg-yellow-400 text-black px-4 py-2 rounded-lg text-sm font-bold" data-date="2024-01-15">
                        <div>今天</div>
                        <div class="text-xs">1/15</div>
                    </button>
                    <button class="calendar-day flex-shrink-0 bg-gray-700 text-white px-4 py-2 rounded-lg text-sm" data-date="2024-01-16">
                        <div>明天</div>
                        <div class="text-xs">1/16</div>
                    </button>
                    <button class="calendar-day flex-shrink-0 bg-gray-700 text-white px-4 py-2 rounded-lg text-sm" data-date="2024-01-17">
                        <div>周三</div>
                        <div class="text-xs">1/17</div>
                    </button>
                    <button class="calendar-day flex-shrink-0 bg-gray-700 text-white px-4 py-2 rounded-lg text-sm" data-date="2024-01-18">
                        <div>周四</div>
                        <div class="text-xs">1/18</div>
                    </button>
                    <button class="calendar-day flex-shrink-0 bg-gray-700 text-white px-4 py-2 rounded-lg text-sm" data-date="2024-01-19">
                        <div>周五</div>
                        <div class="text-xs">1/19</div>
                    </button>
                </div>
            </div>

            <!-- Time Slots -->
            <div>
                <h4 class="text-sm font-bold mb-2 text-gray-400">选择时间段</h4>
                <div class="grid grid-cols-3 gap-2" id="timeSlots">
                    <button class="time-slot available bg-gray-700 text-white px-3 py-2 rounded-lg text-sm" data-time="09:00-10:00">09:00-10:00</button>
                    <button class="time-slot available bg-gray-700 text-white px-3 py-2 rounded-lg text-sm" data-time="10:00-11:00">10:00-11:00</button>
                    <button class="time-slot unavailable" data-time="11:00-12:00">11:00-12:00</button>
                    <button class="time-slot available bg-gray-700 text-white px-3 py-2 rounded-lg text-sm" data-time="14:00-15:00">14:00-15:00</button>
                    <button class="time-slot available bg-gray-700 text-white px-3 py-2 rounded-lg text-sm" data-time="15:00-16:00">15:00-16:00</button>
                    <button class="time-slot available bg-gray-700 text-white px-3 py-2 rounded-lg text-sm" data-time="16:00-17:00">16:00-17:00</button>
                    <button class="time-slot unavailable" data-time="17:00-18:00">17:00-18:00</button>
                    <button class="time-slot available bg-gray-700 text-white px-3 py-2 rounded-lg text-sm" data-time="19:00-20:00">19:00-20:00</button>
                    <button class="time-slot available bg-gray-700 text-white px-3 py-2 rounded-lg text-sm" data-time="20:00-21:00">20:00-21:00</button>
                </div>
            </div>

            <!-- Selected Info -->
            <div class="mt-4 p-3 bg-gray-700 rounded-lg" id="selectedInfo" style="display: none;">
                <p class="text-sm text-gray-400">已选择时间：</p>
                <p class="font-bold" id="selectedDateTime">2024年1月15日 09:00-10:00</p>
                <p class="text-yellow-400 font-bold mt-1">费用：¥180</p>
            </div>
        </div>

        <!-- Reviews -->
        <div class="bg-gray-800 mt-2 p-4">
            <div class="flex items-center justify-between mb-4">
                <h3 class="font-bold">学员评价</h3>
                <button class="text-gray-400 text-sm">查看全部</button>
            </div>
            
            <!-- Review Item -->
            <div class="border-b border-gray-700 pb-4 mb-4">
                <div class="flex items-start gap-3">
                    <img src="https://i.pravatar.cc/40?u=1" class="w-10 h-10 rounded-full" alt="User Avatar">
                    <div class="flex-1">
                        <div class="flex items-center gap-2 mb-1">
                            <span class="font-bold text-sm">学员小王</span>
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-1">张教练非常专业，技术指导很到位，一节课就让我的击球姿势有了明显改善。</p>
                        <p class="text-xs text-gray-400">2024-01-10</p>
                    </div>
                </div>
            </div>

            <!-- Review Item -->
            <div class="border-b border-gray-700 pb-4 mb-4">
                <div class="flex items-start gap-3">
                    <img src="https://i.pravatar.cc/40?u=2" class="w-10 h-10 rounded-full" alt="User Avatar">
                    <div class="flex-1">
                        <div class="flex items-center gap-2 mb-1">
                            <span class="font-bold text-sm">台球爱好者</span>
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                            </div>
                        </div>
                        <p class="text-sm text-gray-300 mb-1">教学很有耐心，会根据我的水平制定训练计划，进步很快！</p>
                        <p class="text-xs text-gray-400">2024-01-08</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bottom Action Bar -->
    <div class="fixed bottom-0 left-0 right-0 bg-gray-900/90 backdrop-blur-md border-t border-gray-700 p-4">
        <div class="flex items-center gap-3">
            <button onclick="contactCoach()" class="flex flex-col items-center gap-1 text-gray-400">
                <i class="fa-solid fa-comment text-xl"></i>
                <span class="text-xs">咨询</span>
            </button>
            <button onclick="favoriteCoach()" class="flex flex-col items-center gap-1 text-gray-400">
                <i class="fa-regular fa-heart text-xl"></i>
                <span class="text-xs">收藏</span>
            </button>
            <button onclick="bookNow()" class="flex-1 bg-gradient-to-r from-yellow-400 to-orange-500 text-black py-3 rounded-lg font-bold glow-yellow ml-3" id="bookButton" disabled>
                请选择时间
            </button>
        </div>
    </div>

    <script>
        let selectedDate = '2024-01-15';
        let selectedTime = null;

        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            setupDateSelector();
            setupTimeSlots();
        });

        function setupDateSelector() {
            document.querySelectorAll('.calendar-day').forEach(day => {
                day.addEventListener('click', function() {
                    const date = this.dataset.date;
                    selectDate(date);
                });
            });
        }

        function setupTimeSlots() {
            document.querySelectorAll('.time-slot.available').forEach(slot => {
                slot.addEventListener('click', function() {
                    const time = this.dataset.time;
                    selectTimeSlot(time);
                });
            });
        }

        function selectDate(date) {
            selectedDate = date;
            selectedTime = null;
            
            // 更新日期选择器样式
            document.querySelectorAll('.calendar-day').forEach(day => {
                if (day.dataset.date === date) {
                    day.className = 'calendar-day selected flex-shrink-0 bg-yellow-400 text-black px-4 py-2 rounded-lg text-sm font-bold';
                } else {
                    day.className = 'calendar-day flex-shrink-0 bg-gray-700 text-white px-4 py-2 rounded-lg text-sm';
                }
            });
            
            // 重置时间选择
            document.querySelectorAll('.time-slot').forEach(slot => {
                slot.classList.remove('selected');
            });
            
            updateBookingInfo();
        }

        function selectTimeSlot(time) {
            selectedTime = time;
            
            // 更新时间段选择器样式
            document.querySelectorAll('.time-slot').forEach(slot => {
                slot.classList.remove('selected');
                if (slot.dataset.time === time) {
                    slot.classList.add('selected');
                }
            });
            
            updateBookingInfo();
        }

        function updateBookingInfo() {
            const selectedInfo = document.getElementById('selectedInfo');
            const selectedDateTime = document.getElementById('selectedDateTime');
            const bookButton = document.getElementById('bookButton');
            
            if (selectedDate && selectedTime) {
                selectedInfo.style.display = 'block';
                selectedDateTime.textContent = `${formatDate(selectedDate)} ${selectedTime}`;
                bookButton.textContent = '立即预约';
                bookButton.disabled = false;
                bookButton.className = 'flex-1 bg-gradient-to-r from-yellow-400 to-orange-500 text-black py-3 rounded-lg font-bold glow-yellow ml-3';
            } else {
                selectedInfo.style.display = 'none';
                bookButton.textContent = '请选择时间';
                bookButton.disabled = true;
                bookButton.className = 'flex-1 bg-gray-600 text-gray-400 py-3 rounded-lg font-bold ml-3 cursor-not-allowed';
            }
        }

        function formatDate(dateStr) {
            const date = new Date(dateStr);
            const month = date.getMonth() + 1;
            const day = date.getDate();
            return `2024年${month}月${day}日`;
        }

        function bookNow() {
            if (!selectedDate || !selectedTime) {
                alert('请选择上课时间');
                return;
            }
            
            // 跳转到预约确认页面
            window.location.href = `booking_confirm.html?coach=1&date=${selectedDate}&time=${selectedTime}`;
        }

        function contactCoach() {
            alert('联系教练');
        }

        function favoriteCoach() {
            const icon = event.target.closest('button').querySelector('i');
            if (icon.classList.contains('fa-regular')) {
                icon.className = 'fa-solid fa-heart text-xl text-red-500';
            } else {
                icon.className = 'fa-regular fa-heart text-xl';
            }
        }

        function shareCoach() {
            alert('分享教练');
        }
    </script>

</body>
</html>
