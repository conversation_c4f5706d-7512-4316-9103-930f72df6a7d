<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>维修进度</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #1A1A1A; color: white; font-family: sans-serif; }
        .timeline {
            position: relative;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 20px;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #F6E05E, #4A5568);
        }
        .timeline-item {
            position: relative;
            padding-left: 60px;
            margin-bottom: 30px;
        }
        .timeline-icon {
            position: absolute;
            left: 8px;
            top: 0;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            z-index: 10;
        }
        .timeline-icon.completed {
            background: #10B981;
            color: white;
        }
        .timeline-icon.current {
            background: #F6E05E;
            color: #1A202C;
            animation: pulse 2s infinite;
        }
        .timeline-icon.pending {
            background: #4A5568;
            color: #9CA3AF;
        }
        @keyframes pulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        .progress-card {
            background: linear-gradient(135deg, #2D3748 0%, #1A202C 100%);
            border: 1px solid #4A5568;
        }
    </style>
</head>
<body class="pb-24">

    <!-- iOS Status Bar -->
    <div class="fixed top-0 left-0 right-0 h-11 bg-black/30 backdrop-blur-sm z-50 px-4 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
        <div class="flex items-center gap-2"><i class="fa-solid fa-signal"></i><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
    </div>
    
    <!-- Header -->
    <div class="fixed top-11 left-0 right-0 bg-gray-900/90 backdrop-blur-md z-40 px-4 py-3 border-b border-gray-700">
        <div class="flex items-center gap-3">
            <button onclick="history.back()" class="text-white"><i class="fa-solid fa-arrow-left text-xl"></i></button>
            <h1 class="text-lg font-bold flex-1">维修进度</h1>
            <button onclick="refreshProgress()" class="text-yellow-400"><i class="fa-solid fa-refresh text-xl"></i></button>
        </div>
    </div>

    <!-- Main Content -->
    <div class="pt-24">
        <!-- Search Bar -->
        <div class="px-4 mb-6">
            <div class="bg-gray-800 rounded-xl p-4">
                <h3 class="font-bold mb-3">查询维修进度</h3>
                <div class="flex gap-3">
                    <input type="text" id="orderNumber" placeholder="请输入订单号" class="flex-1 bg-gray-700 text-white px-3 py-2 rounded-lg outline-none focus:ring-2 focus:ring-yellow-400">
                    <button onclick="searchProgress()" class="bg-yellow-400 text-black px-6 py-2 rounded-lg font-bold">
                        查询
                    </button>
                </div>
            </div>
        </div>

        <!-- Current Repair Progress -->
        <div class="px-4 mb-6" id="progressSection">
            <!-- Order Info -->
            <div class="progress-card rounded-xl p-4 mb-4">
                <div class="flex items-center justify-between mb-4">
                    <h3 class="font-bold text-lg">订单信息</h3>
                    <span class="bg-yellow-400 text-black px-3 py-1 rounded-full text-sm font-bold">维修中</span>
                </div>
                <div class="space-y-2">
                    <div class="flex justify-between">
                        <span class="text-gray-400">订单号</span>
                        <span class="font-mono text-sm">RP20240115001</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">维修类型</span>
                        <span>球杆杆头更换</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">预计完成</span>
                        <span class="text-yellow-400">2024年1月18日</span>
                    </div>
                    <div class="flex justify-between">
                        <span class="text-gray-400">负责技师</span>
                        <span>李师傅</span>
                    </div>
                </div>
            </div>

            <!-- Progress Timeline -->
            <div class="progress-card rounded-xl p-4 mb-4">
                <h3 class="font-bold text-lg mb-4">维修进度</h3>
                <div class="timeline">
                    <!-- Step 1: 已完成 -->
                    <div class="timeline-item">
                        <div class="timeline-icon completed">
                            <i class="fa-solid fa-check"></i>
                        </div>
                        <div class="bg-gray-700 rounded-lg p-3">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-bold text-green-400">申请已提交</h4>
                                <span class="text-xs text-gray-400">2024-01-15 09:30</span>
                            </div>
                            <p class="text-sm text-gray-300">您的维修申请已成功提交，我们将尽快安排技师处理。</p>
                        </div>
                    </div>

                    <!-- Step 2: 已完成 -->
                    <div class="timeline-item">
                        <div class="timeline-icon completed">
                            <i class="fa-solid fa-check"></i>
                        </div>
                        <div class="bg-gray-700 rounded-lg p-3">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-bold text-green-400">已安排取件</h4>
                                <span class="text-xs text-gray-400">2024-01-15 14:20</span>
                            </div>
                            <p class="text-sm text-gray-300">快递员已上门取件，您的球杆正在运送至维修中心。</p>
                            <div class="mt-2 text-xs text-blue-400">
                                <i class="fa-solid fa-truck"></i> 快递单号：SF1234567890
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: 已完成 -->
                    <div class="timeline-item">
                        <div class="timeline-icon completed">
                            <i class="fa-solid fa-check"></i>
                        </div>
                        <div class="bg-gray-700 rounded-lg p-3">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-bold text-green-400">到达维修中心</h4>
                                <span class="text-xs text-gray-400">2024-01-15 18:45</span>
                            </div>
                            <p class="text-sm text-gray-300">您的球杆已安全到达维修中心，技师正在进行初步检测。</p>
                        </div>
                    </div>

                    <!-- Step 4: 已完成 -->
                    <div class="timeline-item">
                        <div class="timeline-icon completed">
                            <i class="fa-solid fa-check"></i>
                        </div>
                        <div class="bg-gray-700 rounded-lg p-3">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-bold text-green-400">检测完成</h4>
                                <span class="text-xs text-gray-400">2024-01-16 10:15</span>
                            </div>
                            <p class="text-sm text-gray-300">技师已完成详细检测，确认需要更换杆头，维修方案已制定。</p>
                            <div class="mt-2 p-2 bg-gray-600 rounded text-xs">
                                <p class="text-yellow-400 font-bold">维修方案：</p>
                                <p>更换优质皮头，调整杆头硬度，重新校准重心</p>
                                <p class="text-green-400 mt-1">费用：¥80（已确认）</p>
                            </div>
                        </div>
                    </div>

                    <!-- Step 5: 进行中 -->
                    <div class="timeline-item">
                        <div class="timeline-icon current">
                            <i class="fa-solid fa-tools"></i>
                        </div>
                        <div class="bg-yellow-400/10 border border-yellow-400/30 rounded-lg p-3">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-bold text-yellow-400">维修进行中</h4>
                                <span class="text-xs text-gray-400">2024-01-16 14:30</span>
                            </div>
                            <p class="text-sm text-gray-300">李师傅正在为您的球杆更换杆头，预计今日完成。</p>
                            <div class="mt-3">
                                <div class="flex items-center justify-between mb-1">
                                    <span class="text-xs text-gray-400">维修进度</span>
                                    <span class="text-xs text-yellow-400">75%</span>
                                </div>
                                <div class="w-full bg-gray-600 rounded-full h-2">
                                    <div class="bg-yellow-400 h-2 rounded-full" style="width: 75%"></div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 6: 待完成 -->
                    <div class="timeline-item">
                        <div class="timeline-icon pending">
                            <i class="fa-solid fa-clipboard-check"></i>
                        </div>
                        <div class="bg-gray-700 rounded-lg p-3 opacity-60">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-bold text-gray-400">质检测试</h4>
                                <span class="text-xs text-gray-400">待进行</span>
                            </div>
                            <p class="text-sm text-gray-400">维修完成后将进行严格的质量检测和性能测试。</p>
                        </div>
                    </div>

                    <!-- Step 7: 待完成 -->
                    <div class="timeline-item">
                        <div class="timeline-icon pending">
                            <i class="fa-solid fa-shipping-fast"></i>
                        </div>
                        <div class="bg-gray-700 rounded-lg p-3 opacity-60">
                            <div class="flex items-center justify-between mb-2">
                                <h4 class="font-bold text-gray-400">安排发货</h4>
                                <span class="text-xs text-gray-400">待进行</span>
                            </div>
                            <p class="text-sm text-gray-400">质检通过后将立即安排发货，您将收到物流信息。</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Info -->
            <div class="progress-card rounded-xl p-4 mb-4">
                <h3 class="font-bold text-lg mb-3">联系方式</h3>
                <div class="space-y-3">
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400">技师电话</span>
                        <a href="tel:13800138000" class="text-yellow-400 font-bold">138-0013-8000</a>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400">客服热线</span>
                        <a href="tel:************" class="text-yellow-400 font-bold">************</a>
                    </div>
                    <div class="flex items-center justify-between">
                        <span class="text-gray-400">工作时间</span>
                        <span>周一至周日 9:00-21:00</span>
                    </div>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="grid grid-cols-2 gap-4">
                <button onclick="contactTechnician()" class="bg-gray-700 text-white py-3 rounded-lg font-bold">
                    <i class="fa-solid fa-phone mr-2"></i>联系技师
                </button>
                <button onclick="viewDetails()" class="bg-yellow-400 text-black py-3 rounded-lg font-bold">
                    <i class="fa-solid fa-eye mr-2"></i>查看详情
                </button>
            </div>
        </div>

        <!-- No Results State (Hidden by default) -->
        <div class="hidden text-center py-20" id="noResults">
            <i class="fa-solid fa-search text-6xl text-gray-600 mb-4"></i>
            <p class="text-gray-400 text-lg mb-4">未找到相关订单</p>
            <p class="text-gray-500 text-sm mb-6">请检查订单号是否正确</p>
            <button onclick="goToRepairService()" class="bg-yellow-400 text-black px-6 py-2 rounded-lg font-bold">
                申请维修
            </button>
        </div>
    </div>

    <!-- Bottom Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 h-20 bg-gray-900/80 backdrop-blur-md flex justify-around items-center text-gray-400 border-t border-gray-700">
        <a href="home_rich.html" class="flex flex-col items-center gap-1"><i class="fa-solid fa-house text-xl"></i><span class="text-xs">首页</span></a>
        <a href="mall_list_enhanced.html" class="flex flex-col items-center gap-1"><i class="fa-solid fa-store text-xl"></i><span class="text-xs">商城</span></a>
        <a href="#" class="w-16 h-16 -mt-8 bg-yellow-400 rounded-full flex items-center justify-center text-black shadow-lg"><i class="fa-solid fa-qrcode text-3xl"></i></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-file-invoice text-xl"></i><span class="text-xs">订单</span></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-user text-xl"></i><span class="text-xs">我的</span></a>
    </div>

    <script>
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            // 检查是否有URL参数中的订单号
            const urlParams = new URLSearchParams(window.location.search);
            const orderNumber = urlParams.get('order');
            if (orderNumber) {
                document.getElementById('orderNumber').value = orderNumber;
                searchProgress();
            }
        });

        function searchProgress() {
            const orderNumber = document.getElementById('orderNumber').value.trim();
            
            if (!orderNumber) {
                alert('请输入订单号');
                return;
            }

            // 模拟搜索过程
            showLoading();
            
            setTimeout(() => {
                hideLoading();
                
                // 模拟搜索结果
                if (orderNumber === 'RP20240115001' || orderNumber.toLowerCase() === 'rp20240115001') {
                    showProgressSection();
                } else {
                    showNoResults();
                }
            }, 1000);
        }

        function showLoading() {
            const button = document.querySelector('button[onclick="searchProgress()"]');
            button.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i>';
            button.disabled = true;
        }

        function hideLoading() {
            const button = document.querySelector('button[onclick="searchProgress()"]');
            button.innerHTML = '查询';
            button.disabled = false;
        }

        function showProgressSection() {
            document.getElementById('progressSection').classList.remove('hidden');
            document.getElementById('noResults').classList.add('hidden');
        }

        function showNoResults() {
            document.getElementById('progressSection').classList.add('hidden');
            document.getElementById('noResults').classList.remove('hidden');
        }

        function refreshProgress() {
            // 模拟刷新
            const icon = document.querySelector('button[onclick="refreshProgress()"] i');
            icon.classList.add('fa-spin');
            
            setTimeout(() => {
                icon.classList.remove('fa-spin');
                showNotification('进度已更新');
            }, 1000);
        }

        function contactTechnician() {
            if (confirm('确定要拨打技师电话吗？')) {
                window.location.href = 'tel:13800138000';
            }
        }

        function viewDetails() {
            window.location.href = 'repair_detail.html?id=RP20240115001';
        }

        function goToRepairService() {
            window.location.href = 'repair_service.html';
        }

        function showNotification(message) {
            const notification = document.createElement('div');
            notification.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-4 py-2 rounded-lg z-50';
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    </script>

</body>
</html>
