<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #F7F8FA; color: #333; font-family: sans-serif; }
    </style>
</head>
<body class="pb-24">
    <!-- iOS Status Bar & App Header -->
    <div class="fixed top-0 left-0 right-0 bg-white/80 backdrop-blur-md z-50">
        <div class="h-11 px-4 flex justify-between items-center text-sm font-semibold text-black">
            <span>9:41</span>
            <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
            <div class="flex items-center gap-2">
                <i class="fa-solid fa-signal"></i>
                <i class="fa-solid fa-wifi"></i>
                <i class="fa-solid fa-battery-full"></i>
            </div>
        </div>
        <div class="h-12 flex items-center justify-between px-4">
            <h1 class="font-bold text-lg text-black">小铁自助台球</h1>
             <button class="text-gray-500 text-sm">筛选 <i class="fa-solid fa-chevron-down text-xs"></i></button>
        </div>
    </div>
    
    <!-- Main Content -->
    <div class="pt-24">
        <!-- Tabs -->
        <div class="px-4 flex border-b">
             <button class="py-2 px-4 text-black font-bold border-b-2 border-yellow-400">进行中</button>
             <button class="py-2 px-4 text-gray-500 font-semibold">已完成</button>
        </div>
        
        <!-- Empty State -->
        <div class="text-center pt-24 pb-8">
            <div class="w-24 h-24 bg-gray-200 rounded-full mx-auto flex items-center justify-center">
                <i class="fa-solid fa-file-lines text-5xl text-gray-400"></i>
            </div>
            <p class="mt-4 text-gray-400">暂无数据~</p>
        </div>

    </div>

     <!-- Bottom Banner -->
    <div class="px-4 absolute bottom-24 w-full">
         <div class="bg-gradient-to-r from-yellow-300 to-yellow-500 rounded-2xl p-4 flex justify-between items-center shadow-lg">
             <div>
                <h3 class="font-bold text-lg text-black">招募合伙人</h3>
                <p class="text-sm text-black/70"># 无人值守 创业首选 #</p>
             </div>
             <div class="relative">
                <i class="fa-solid fa-puzzle-piece text-5xl text-white/50 transform -rotate-12"></i>
                <a href="#" class="absolute -right-3 top-1/2 -translate-y-1/2 w-8 h-8 bg-black/80 rounded-full flex items-center justify-center text-white">
                     <i class="fa-solid fa-arrow-right"></i>
                </a>
             </div>
         </div>
    </div>
    
    <!-- Bottom Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 h-20 bg-white/80 backdrop-blur-md flex justify-around items-center text-gray-400 border-t border-gray-200">
         <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-house text-xl"></i><span class="text-xs">首页</span></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-store text-xl"></i><span class="text-xs">门店</span></a>
        <a href="#" class="w-16 h-16 -mt-8 bg-yellow-400 rounded-full flex items-center justify-center text-black shadow-lg"><i class="fa-solid fa-coins text-3xl"></i></a>
        <a href="#" class="flex flex-col items-center gap-1 text-yellow-500"><i class="fa-solid fa-file-invoice text-xl"></i><span class="text-xs font-bold">订单</span></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-user text-xl"></i><span class="text-xs">我的</span></a>
    </div>

</body>
</html>