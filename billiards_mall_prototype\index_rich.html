<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>小铁台球商城 - 完整功能原型展示</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            background-color: #f0f2f5;
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            margin: 0;
            padding: 20px;
        }

        /* 页面网格布局 */
        .pages-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(420px, 1fr));
            gap: 30px;
            max-width: 2400px;
            margin: 0 auto;
            padding: 20px;
            justify-items: center;
        }

        .page-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            max-width: 420px;
        }
        .mobile-frame {
            width: 393px; /* iPhone 15 Pro width */
            background-color: #1a1a1a;
            border-radius: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            padding: 12px;
            box-sizing: border-box;
            border: 4px solid #444;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .mobile-frame:hover {
            transform: translateY(-8px);
            box-shadow: 0 25px 50px rgba(0,0,0,0.4);
        }

        .mobile-screen {
            width: 100%;
            height: 852px; /* iPhone 15 Pro height */
            background-color: #000;
            border-radius: 28px;
            overflow: hidden;
            position: relative;
        }
        .mobile-screen iframe {
            width: 100%;
            height: 100%;
            border: none;
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #3B82F6;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
            transition: all 0.3s ease;
            z-index: 1000;
        }

        .back-to-top:hover {
            background: #2563EB;
            transform: translateY(-2px);
        }

        /* 平滑滚动 */
        html {
            scroll-behavior: smooth;
        }

        /* 导航链接样式 */
        a[href^="#"]:hover {
            color: #3B82F6 !important;
            text-decoration: underline !important;
        }
        h1, h2 {
            text-align: center;
            font-weight: bold;
        }
        h1 { font-size: 2rem; color: #333; margin-bottom: 10px; }
        h2 { font-size: 1rem; color: #555; margin-bottom: 15px; }

        /* 响应式调整 */
        @media (max-width: 480px) {
            .pages-grid {
                grid-template-columns: 1fr;
                gap: 15px;
                padding: 10px;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            }

            .mobile-frame {
                width: 280px;
                padding: 8px;
                border-radius: 30px;
                border: 3px solid #444;
            }

            .mobile-screen {
                height: 607px; /* 保持iPhone 15 Pro比例 */
                border-radius: 22px;
            }

            h1 { font-size: 1.3rem; }
            h2 { font-size: 0.85rem; }
        }

        @media (min-width: 481px) and (max-width: 768px) {
            .pages-grid {
                grid-template-columns: 1fr;
                gap: 20px;
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            }

            .mobile-frame {
                width: 320px;
                padding: 10px;
            }

            .mobile-screen {
                height: 693px; /* 保持iPhone 15 Pro比例 */
            }
        }

        @media (min-width: 769px) and (max-width: 1199px) {
            .pages-grid {
                grid-template-columns: repeat(2, 1fr);
                gap: 25px;
            }

            .mobile-frame {
                width: 350px;
            }

            .mobile-screen {
                height: 758px; /* 保持iPhone 15 Pro比例 */
            }
        }

        @media (min-width: 1200px) and (max-width: 1799px) {
            .pages-grid {
                grid-template-columns: repeat(3, 1fr);
            }
        }

        @media (min-width: 1800px) and (max-width: 2399px) {
            .pages-grid {
                grid-template-columns: repeat(4, 1fr);
            }
        }

        @media (min-width: 2400px) and (max-width: 2999px) {
            .pages-grid {
                grid-template-columns: repeat(5, 1fr);
            }
        }

        @media (min-width: 3000px) {
            .pages-grid {
                grid-template-columns: repeat(6, 1fr);
            }
        }
    </style>
</head>
<body>

    <h1>🎱 小铁台球商城 - 完整功能原型展示</h1>
    <p style="text-align: center; color: #666; margin-bottom: 10px; font-size: 16px;">
        包含商城、课程预约、维修服务、回收服务、个人中心等18个核心页面
    </p>
    <p style="text-align: center; color: #888; margin-bottom: 20px; font-size: 14px;">
        📱 iPhone 15 Pro 尺寸展示 (393×852px) | 响应式网格布局
    </p>

    <!-- 快速导航 -->
    <div style="max-width: 1000px; margin: 0 auto 40px; padding: 20px; background: white; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
        <h2 style="color: #333; margin-bottom: 20px; text-align: center;">📋 快速导航</h2>
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
            <div style="padding: 15px; border-left: 4px solid #3B82F6; background: #F8FAFC;">
                <h3 style="color: #1E40AF; margin-bottom: 10px; font-size: 16px;">🛒 商城模块</h3>
                <div style="display: flex; flex-direction: column; gap: 5px;">
                    <a href="#section-1" style="color: #64748B; text-decoration: none; font-size: 14px;">• 首页 (丰富版)</a>
                    <a href="#section-6" style="color: #64748B; text-decoration: none; font-size: 14px;">• 商品列表 (增强版)</a>
                    <a href="#section-3" style="color: #64748B; text-decoration: none; font-size: 14px;">• 商品详情</a>
                    <a href="#section-4" style="color: #64748B; text-decoration: none; font-size: 14px;">• 购物车</a>
                    <a href="#section-5" style="color: #64748B; text-decoration: none; font-size: 14px;">• 搜索页面</a>
                </div>
            </div>

            <div style="padding: 15px; border-left: 4px solid #10B981; background: #F0FDF4;">
                <h3 style="color: #047857; margin-bottom: 10px; font-size: 16px;">📅 课程预约</h3>
                <div style="display: flex; flex-direction: column; gap: 5px;">
                    <a href="#section-7" style="color: #64748B; text-decoration: none; font-size: 14px;">• 教练列表</a>
                    <a href="#section-8" style="color: #64748B; text-decoration: none; font-size: 14px;">• 教练详情</a>
                    <a href="#section-9" style="color: #64748B; text-decoration: none; font-size: 14px;">• 预约确认</a>
                    <a href="#section-10" style="color: #64748B; text-decoration: none; font-size: 14px;">• 预约成功</a>
                    <a href="#section-11" style="color: #64748B; text-decoration: none; font-size: 14px;">• 我的预约</a>
                </div>
            </div>

            <div style="padding: 15px; border-left: 4px solid #F59E0B; background: #FFFBEB;">
                <h3 style="color: #D97706; margin-bottom: 10px; font-size: 16px;">🔧 维修/回收</h3>
                <div style="display: flex; flex-direction: column; gap: 5px;">
                    <a href="#section-12" style="color: #64748B; text-decoration: none; font-size: 14px;">• 维修服务</a>
                    <a href="#section-13" style="color: #64748B; text-decoration: none; font-size: 14px;">• 申请表单</a>
                    <a href="#section-14" style="color: #64748B; text-decoration: none; font-size: 14px;">• 进度查询</a>
                    <a href="#section-15" style="color: #64748B; text-decoration: none; font-size: 14px;">• 回收服务</a>
                </div>
            </div>

            <div style="padding: 15px; border-left: 4px solid #8B5CF6; background: #FAF5FF;">
                <h3 style="color: #7C3AED; margin-bottom: 10px; font-size: 16px;">👤 个人中心</h3>
                <div style="display: flex; flex-direction: column; gap: 5px;">
                    <a href="#section-16" style="color: #64748B; text-decoration: none; font-size: 14px;">• 个人中心</a>
                    <a href="#section-17" style="color: #64748B; text-decoration: none; font-size: 14px;">• 会员中心</a>
                    <a href="#section-18" style="color: #64748B; text-decoration: none; font-size: 14px;">• 分销中心</a>
                </div>
            </div>
        </div>
    </div>

    <!-- 页面展示网格 -->
    <div class="pages-grid">
        <div class="page-item" id="section-1">
            <h2>1. 首页 (丰富版)</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="home_rich.html"></iframe>
                </div>
            </div>
        </div>

        <div class="page-item" id="section-2">
            <h2>2. 商品列表页</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="mall_list.html"></iframe>
                </div>
            </div>
        </div>

        <div class="page-item" id="section-3">
            <h2>3. 商品详情页</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="product_detail.html"></iframe>
                </div>
            </div>
        </div>

        <div class="page-item" id="section-4">
            <h2>4. 购物车</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="cart.html"></iframe>
                </div>
            </div>
        </div>

        <div class="page-item" id="section-5">
            <h2>5. 搜索页面</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="search.html"></iframe>
                </div>
            </div>
        </div>

        <div class="page-item" id="section-6">
            <h2>6. 增强版商品列表</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="mall_list_enhanced.html"></iframe>
                </div>
            </div>
        </div>

        <div class="page-item" id="section-7">
            <h2>7. 教练列表</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="coach_list.html"></iframe>
                </div>
            </div>
        </div>

        <div class="page-item" id="section-8">
            <h2>8. 教练详情</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="coach_detail.html"></iframe>
                </div>
            </div>
        </div>

        <div class="page-item" id="section-9">
            <h2>9. 预约确认</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="booking_confirm.html"></iframe>
                </div>
            </div>
        </div>

        <div class="page-item" id="section-10">
            <h2>10. 预约成功</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="booking_success.html"></iframe>
                </div>
            </div>
        </div>

        <div class="page-item" id="section-11">
            <h2>11. 我的预约</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="my_bookings.html"></iframe>
                </div>
            </div>
        </div>

        <div class="page-item" id="section-12">
            <h2>12. 维修服务</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="repair_service.html"></iframe>
                </div>
            </div>
        </div>

        <div class="page-item" id="section-13">
            <h2>13. 维修申请</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="repair_form.html"></iframe>
                </div>
            </div>
        </div>

        <div class="page-item" id="section-14">
            <h2>14. 维修进度</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="repair_progress.html"></iframe>
                </div>
            </div>
        </div>

        <div class="page-item" id="section-15">
            <h2>15. 回收服务</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="recycle_service.html"></iframe>
                </div>
            </div>
        </div>

        <div class="page-item" id="section-16">
            <h2>16. 个人中心</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="profile_enhanced.html"></iframe>
                </div>
            </div>
        </div>

        <div class="page-item" id="section-17">
            <h2>17. 会员中心</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="membership_center.html"></iframe>
                </div>
            </div>
        </div>

        <div class="page-item" id="section-18">
            <h2>18. 分销中心</h2>
            <div class="mobile-frame">
                <div class="mobile-screen">
                    <iframe src="distribution_center.html"></iframe>
                </div>
            </div>
        </div>
    </div> <!-- 关闭 pages-grid -->

    <!-- 功能说明 -->
    <div style="max-width: 800px; margin: 40px 0; padding: 20px; background: white; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.1);">
        <h2 style="color: #333; margin-bottom: 20px;">📱 功能模块说明</h2>

        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 20px;">
            <div style="padding: 15px; border-left: 4px solid #3B82F6; background: #F8FAFC;">
                <h3 style="color: #1E40AF; margin-bottom: 10px;">🛒 商城模块</h3>
                <ul style="color: #64748B; font-size: 14px; line-height: 1.6;">
                    <li>• 丰富的首页设计（轮播图、快捷入口）</li>
                    <li>• 商品列表（分类筛选、排序功能）</li>
                    <li>• 商品详情（规格选择、分享功能）</li>
                    <li>• 购物车管理（数量调整、结算）</li>
                    <li>• 智能搜索（建议、历史记录）</li>
                </ul>
            </div>

            <div style="padding: 15px; border-left: 4px solid #10B981; background: #F0FDF4;">
                <h3 style="color: #047857; margin-bottom: 10px;">📅 课程预约模块</h3>
                <ul style="color: #64748B; font-size: 14px; line-height: 1.6;">
                    <li>• 教练列表（筛选、评分展示）</li>
                    <li>• 教练详情（日历选择、时间预约）</li>
                    <li>• 预约确认（信息填写、支付）</li>
                    <li>• 预约管理（状态跟踪、改期取消）</li>
                    <li>• 成功页面（确认信息、重要提醒）</li>
                </ul>
            </div>

            <div style="padding: 15px; border-left: 4px solid #F59E0B; background: #FFFBEB;">
                <h3 style="color: #D97706; margin-bottom: 10px;">🔧 维修/回收模块</h3>
                <ul style="color: #64748B; font-size: 14px; line-height: 1.6;">
                    <li>• 维修服务（服务介绍、流程说明）</li>
                    <li>• 维修申请（详细表单、图片上传）</li>
                    <li>• 进度查询（时间线设计、实时更新）</li>
                    <li>• 回收服务（价格参考、环保理念）</li>
                    <li>• 透明的服务跟踪系统</li>
                </ul>
            </div>

            <div style="padding: 15px; border-left: 4px solid #8B5CF6; background: #FAF5FF;">
                <h3 style="color: #7C3AED; margin-bottom: 10px;">👤 个人中心模块</h3>
                <ul style="color: #64748B; font-size: 14px; line-height: 1.6;">
                    <li>• 增强版个人中心（用户信息、统计）</li>
                    <li>• 会员中心（等级体系、特权展示）</li>
                    <li>• 分销中心（佣金规则、推广工具）</li>
                    <li>• 积分系统（签到、活动、兑换）</li>
                    <li>• 完整的用户成长体系</li>
                </ul>
            </div>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 12px; color: white;">
            <h3 style="margin-bottom: 15px; color: white;">🎯 设计特色</h3>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div>
                    <strong>🎨 视觉设计</strong><br>
                    <small>暗色主题、黄色主色调、现代渐变效果</small>
                </div>
                <div>
                    <strong>📱 移动优先</strong><br>
                    <small>完全适配iOS/Android设备，流畅交互</small>
                </div>
                <div>
                    <strong>🚀 用户体验</strong><br>
                    <small>快速导航、智能推荐、一键操作</small>
                </div>
                <div>
                    <strong>⚡ 技术实现</strong><br>
                    <small>HTML5 + Tailwind CSS + 原生JavaScript</small>
                </div>
            </div>
        </div>
    </div>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" onclick="scrollToTop()" title="返回顶部">
        ↑
    </button>

    <script>
        // 返回顶部功能
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 显示/隐藏返回顶部按钮
        window.addEventListener('scroll', function() {
            const backToTopBtn = document.querySelector('.back-to-top');
            if (window.pageYOffset > 300) {
                backToTopBtn.style.display = 'block';
            } else {
                backToTopBtn.style.display = 'none';
            }
        });

        // 初始隐藏返回顶部按钮
        document.addEventListener('DOMContentLoaded', function() {
            const backToTopBtn = document.querySelector('.back-to-top');
            backToTopBtn.style.display = 'none';
        });
    </script>

</body>
</html>