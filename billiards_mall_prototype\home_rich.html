<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页 (丰富版)</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #1A1A1A; color: white; font-family: sans-serif; }
        .glow-yellow { box-shadow: 0 0 15px rgba(255, 215, 0, 0.5); }
        .line-clamp-2 {
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        .carousel-indicator-active {
            background-color: rgba(255, 255, 255, 1) !important;
        }
    </style>
</head>
<body class="pb-24">

    <!-- iOS Status Bar -->
    <div class="fixed top-0 left-0 right-0 h-11 bg-black/30 backdrop-blur-sm z-50 px-4 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
        <div class="flex items-center gap-2"><i class="fa-solid fa-signal"></i><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
    </div>
    
    <!-- Main Content -->
    <div class="pt-11">
        <!-- User Profile Card -->
        <div class="p-4 bg-gray-800">
            <div class="flex items-center gap-3">
                <img src="https://i.pravatar.cc/50?u=a042581f4e29026704d" alt="avatar" class="w-12 h-12 rounded-full border-2 border-yellow-400">
                <div>
                    <p class="font-bold text-lg">明亮的杰瑞 <i class="fa-solid fa-feather text-blue-400"></i></p>
                    <p class="text-xs text-gray-400">欢迎回来，开启你的台球之旅</p>
                </div>
            </div>
        </div>

        <!-- Banner Carousel -->
        <div class="relative h-40 overflow-hidden">
            <div class="flex transition-transform duration-500 ease-in-out" id="bannerCarousel">
                <!-- Banner 1 -->
                <div class="w-full flex-shrink-0 relative cursor-pointer" onclick="openBannerLink(0)">
                    <img src="https://images.unsplash.com/photo-1574116035011-e68651815f43?q=80&w=1200&auto=format&fit=crop" class="w-full h-full object-cover" alt="Banner Ad">
                    <div class="absolute inset-0 bg-gradient-to-r from-black/50 to-transparent"></div>
                    <div class="absolute bottom-4 left-4">
                        <h2 class="text-2xl font-black text-white drop-shadow-lg">冠军之选</h2>
                        <p class="text-sm text-white/90">新款碳素球杆，限时8折</p>
                    </div>
                </div>
                <!-- Banner 2 -->
                <div class="w-full flex-shrink-0 relative cursor-pointer" onclick="openBannerLink(1)">
                    <img src="https://images.unsplash.com/photo-1557996739-b883c79d1297?q=80&w=1200&auto=format&fit=crop" class="w-full h-full object-cover" alt="Banner Ad">
                    <div class="absolute inset-0 bg-gradient-to-r from-black/50 to-transparent"></div>
                    <div class="absolute bottom-4 left-4">
                        <h2 class="text-2xl font-black text-white drop-shadow-lg">专业装备</h2>
                        <p class="text-sm text-white/90">进口台球配件，品质保证</p>
                    </div>
                </div>
                <!-- Banner 3 -->
                <div class="w-full flex-shrink-0 relative cursor-pointer" onclick="openBannerLink(2)">
                    <img src="https://images.unsplash.com/photo-1631181827493-276949033331?q=80&w=1200&auto=format&fit=crop" class="w-full h-full object-cover" alt="Banner Ad">
                    <div class="absolute inset-0 bg-gradient-to-r from-black/50 to-transparent"></div>
                    <div class="absolute bottom-4 left-4">
                        <h2 class="text-2xl font-black text-white drop-shadow-lg">精品球桌</h2>
                        <p class="text-sm text-white/90">家用商用台球桌，全场包邮</p>
                    </div>
                </div>
            </div>
            <!-- Carousel Indicators -->
            <div class="absolute bottom-2 right-4 flex gap-1">
                <div class="w-2 h-2 rounded-full bg-white/50" id="indicator-0"></div>
                <div class="w-2 h-2 rounded-full bg-white/50" id="indicator-1"></div>
                <div class="w-2 h-2 rounded-full bg-white/50" id="indicator-2"></div>
            </div>
        </div>

        <!-- Function Navigation Grid (2x2 金刚区) -->
        <div class="grid grid-cols-2 gap-4 px-4 py-4">
            <a href="mall_list.html" class="flex flex-col items-center gap-3 bg-gray-800/50 rounded-2xl p-4 backdrop-blur-sm">
                <div class="w-16 h-16 rounded-2xl bg-gradient-to-br from-yellow-400 via-yellow-500 to-orange-500 flex items-center justify-center text-black shadow-2xl glow-yellow">
                    <i class="fa-solid fa-store text-3xl drop-shadow-lg"></i>
                </div>
                <span class="text-sm font-bold text-yellow-400">小铁商城</span>
            </a>
            <a href="coach_list.html" class="flex flex-col items-center gap-3 bg-gray-800/50 rounded-2xl p-4 backdrop-blur-sm">
                <div class="w-16 h-16 rounded-2xl bg-gradient-to-br from-blue-400 via-blue-500 to-purple-500 flex items-center justify-center text-white shadow-2xl">
                    <i class="fa-solid fa-calendar-check text-3xl drop-shadow-lg"></i>
                </div>
                <span class="text-sm font-bold text-blue-400">课程预约</span>
            </a>
            <a href="repair_service.html" class="flex flex-col items-center gap-3 bg-gray-800/50 rounded-2xl p-4 backdrop-blur-sm">
                <div class="w-16 h-16 rounded-2xl bg-gradient-to-br from-green-400 via-green-500 to-teal-500 flex items-center justify-center text-white shadow-2xl">
                    <i class="fa-solid fa-screwdriver-wrench text-3xl drop-shadow-lg"></i>
                </div>
                <span class="text-sm font-bold text-green-400">申请维修</span>
            </a>
            <a href="recycle_service.html" class="flex flex-col items-center gap-3 bg-gray-800/50 rounded-2xl p-4 backdrop-blur-sm">
                <div class="w-16 h-16 rounded-2xl bg-gradient-to-br from-purple-400 via-purple-500 to-pink-500 flex items-center justify-center text-white shadow-2xl">
                    <i class="fa-solid fa-recycle text-3xl drop-shadow-lg"></i>
                </div>
                <span class="text-sm font-bold text-purple-400">球杆回收</span>
            </a>
        </div>

        <!-- Product Recommendation (瀑布流布局) -->
        <div class="px-4 pt-4 border-t border-gray-700">
            <h3 class="text-lg font-bold mb-4 flex items-center gap-2">
                <i class="fa-solid fa-fire text-yellow-400"></i>
                为你推荐
            </h3>
            <!-- 瀑布流容器 -->
            <div class="columns-2 gap-3 space-y-3">
                <!-- Product Card 1 -->
                <div class="bg-gray-800 rounded-xl overflow-hidden break-inside-avoid mb-3 shadow-lg hover:shadow-xl transition-shadow duration-300" onclick="openProductDetail(1)">
                    <img src="https://images.unsplash.com/photo-1557996739-b883c79d1297?q=80&w=600&auto=format&fit=crop" class="w-full h-40 object-cover" alt="Product">
                    <div class="p-3">
                        <p class="text-sm font-semibold line-clamp-2">Cuppa-JR 职业级枫木球杆</p>
                        <p class="text-lg font-bold text-yellow-400 mt-2">¥ <span class="text-2xl">1280</span></p>
                        <div class="flex items-center gap-1 mt-1">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                            </div>
                            <span class="text-xs text-gray-400">已售128</span>
                        </div>
                    </div>
                </div>

                <!-- Product Card 2 -->
                <div class="bg-gray-800 rounded-xl overflow-hidden break-inside-avoid mb-3 shadow-lg hover:shadow-xl transition-shadow duration-300" onclick="openProductDetail(2)">
                    <img src="https://images.unsplash.com/photo-1631181827493-276949033331?q=80&w=600&auto=format&fit=crop" class="w-full h-56 object-cover" alt="Product">
                    <div class="p-3">
                        <p class="text-sm font-semibold line-clamp-2">进口比利时水晶球 标准版 16颗装</p>
                        <p class="text-lg font-bold text-yellow-400 mt-2">¥ <span class="text-2xl">899</span></p>
                        <div class="flex items-center gap-1 mt-1">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-regular fa-star"></i>
                            </div>
                            <span class="text-xs text-gray-400">已售89</span>
                        </div>
                    </div>
                </div>

                <!-- Product Card 3 -->
                <div class="bg-gray-800 rounded-xl overflow-hidden break-inside-avoid mb-3 shadow-lg hover:shadow-xl transition-shadow duration-300" onclick="openProductDetail(3)">
                    <img src="https://plus.unsplash.com/premium_photo-1661662991873-6784a0c897f2?q=80&w=600&auto=format&fit=crop" class="w-full h-32 object-cover" alt="Product">
                    <div class="p-3">
                        <p class="text-sm font-semibold line-clamp-2">专业防滑台球手套</p>
                        <p class="text-lg font-bold text-yellow-400 mt-2">¥ <span class="text-2xl">49</span></p>
                        <div class="flex items-center gap-1 mt-1">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                            </div>
                            <span class="text-xs text-gray-400">已售256</span>
                        </div>
                    </div>
                </div>

                <!-- Product Card 4 -->
                <div class="bg-gray-800 rounded-xl overflow-hidden break-inside-avoid mb-3 shadow-lg hover:shadow-xl transition-shadow duration-300" onclick="openProductDetail(4)">
                    <img src="https://images.unsplash.com/photo-1594759881342-a059d18e5898?q=80&w=600&auto=format&fit=crop" class="w-full h-44 object-cover" alt="Product">
                    <div class="p-3">
                        <p class="text-sm font-semibold line-clamp-2">大师级巧克粉 防滑专用</p>
                        <p class="text-lg font-bold text-yellow-400 mt-2">¥ <span class="text-2xl">128</span></p>
                        <div class="flex items-center gap-1 mt-1">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-regular fa-star"></i>
                            </div>
                            <span class="text-xs text-gray-400">已售67</span>
                        </div>
                    </div>
                </div>

                <!-- Product Card 5 -->
                <div class="bg-gray-800 rounded-xl overflow-hidden break-inside-avoid mb-3 shadow-lg hover:shadow-xl transition-shadow duration-300" onclick="openProductDetail(5)">
                    <img src="https://images.unsplash.com/photo-1559523161-0d5b3d99d32b?q=80&w=600&auto=format&fit=crop" class="w-full h-48 object-cover" alt="Product">
                    <div class="p-3">
                        <p class="text-sm font-semibold line-clamp-2">专业台球桌 家用标准版</p>
                        <p class="text-lg font-bold text-yellow-400 mt-2">¥ <span class="text-2xl">3999</span></p>
                        <div class="flex items-center gap-1 mt-1">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                            </div>
                            <span class="text-xs text-gray-400">已售23</span>
                        </div>
                    </div>
                </div>

                <!-- Product Card 6 -->
                <div class="bg-gray-800 rounded-xl overflow-hidden break-inside-avoid mb-3 shadow-lg hover:shadow-xl transition-shadow duration-300" onclick="openProductDetail(6)">
                    <img src="https://images.unsplash.com/photo-1574116035011-e68651815f43?q=80&w=600&auto=format&fit=crop" class="w-full h-36 object-cover" alt="Product">
                    <div class="p-3">
                        <p class="text-sm font-semibold line-clamp-2">碳纤维球杆头 专业级</p>
                        <p class="text-lg font-bold text-yellow-400 mt-2">¥ <span class="text-2xl">299</span></p>
                        <div class="flex items-center gap-1 mt-1">
                            <div class="flex text-yellow-400 text-xs">
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-solid fa-star"></i>
                                <i class="fa-regular fa-star"></i>
                            </div>
                            <span class="text-xs text-gray-400">已售156</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- Bottom Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 h-20 bg-gray-900/80 backdrop-blur-md flex justify-around items-center text-gray-400 border-t border-gray-700">
        <a href="#" class="flex flex-col items-center gap-1 text-yellow-400"><i class="fa-solid fa-house text-xl"></i><span class="text-xs font-bold">首页</span></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-store text-xl"></i><span class="text-xs">商城</span></a>
        <a href="#" class="w-16 h-16 -mt-8 bg-yellow-400 rounded-full flex items-center justify-center text-black shadow-lg glow-yellow"><i class="fa-solid fa-qrcode text-3xl"></i></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-file-invoice text-xl"></i><span class="text-xs">订单</span></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-user text-xl"></i><span class="text-xs">我的</span></a>
    </div>

    <script>
        // 轮播图自动播放
        let currentSlide = 0;
        const totalSlides = 3;
        const carousel = document.getElementById('bannerCarousel');

        function updateCarousel() {
            carousel.style.transform = `translateX(-${currentSlide * 100}%)`;

            // 更新指示器
            for (let i = 0; i < totalSlides; i++) {
                const indicator = document.getElementById(`indicator-${i}`);
                if (i === currentSlide) {
                    indicator.classList.add('carousel-indicator-active');
                } else {
                    indicator.classList.remove('carousel-indicator-active');
                }
            }
        }

        function nextSlide() {
            currentSlide = (currentSlide + 1) % totalSlides;
            updateCarousel();
        }

        // 自动播放
        setInterval(nextSlide, 4000);

        // 初始化
        updateCarousel();

        // 商品详情跳转
        function openProductDetail(productId) {
            window.location.href = `product_detail.html?id=${productId}`;
        }

        // Banner点击跳转
        function openBannerLink(bannerId) {
            // 根据不同banner跳转到不同页面
            switch(bannerId) {
                case 0:
                    window.location.href = 'product_detail.html?id=1';
                    break;
                case 1:
                    window.location.href = 'mall_list.html?category=accessories';
                    break;
                case 2:
                    window.location.href = 'mall_list.html?category=tables';
                    break;
            }
        }
    </script>

</body>
</html>