<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>确认预约</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #1A1A1A; color: white; font-family: sans-serif; }
        .glow-yellow { box-shadow: 0 0 15px rgba(255, 215, 0, 0.5); }
    </style>
</head>
<body class="pb-32">

    <!-- iOS Status Bar -->
    <div class="fixed top-0 left-0 right-0 h-11 bg-black/30 backdrop-blur-sm z-50 px-4 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
        <div class="flex items-center gap-2"><i class="fa-solid fa-signal"></i><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
    </div>
    
    <!-- Header -->
    <div class="fixed top-11 left-0 right-0 bg-gray-900/90 backdrop-blur-md z-40 px-4 py-3 border-b border-gray-700">
        <div class="flex items-center gap-3">
            <button onclick="history.back()" class="text-white"><i class="fa-solid fa-arrow-left text-xl"></i></button>
            <h1 class="text-lg font-bold">确认预约</h1>
        </div>
    </div>

    <!-- Main Content -->
    <div class="pt-24">
        <!-- Coach Info -->
        <div class="bg-gray-800 p-4 mx-4 rounded-xl mb-4">
            <div class="flex gap-4">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=80&auto=format&fit=crop" class="w-16 h-16 rounded-full object-cover border-2 border-yellow-400" alt="Coach Avatar">
                <div class="flex-1">
                    <h3 class="text-lg font-bold">张教练</h3>
                    <div class="bg-gradient-to-r from-yellow-400 to-orange-500 text-black inline-block px-2 py-1 rounded-full text-xs font-bold mt-1">
                        国家一级教练
                    </div>
                    <div class="flex items-center gap-2 mt-2">
                        <div class="flex text-yellow-400 text-sm">
                            <i class="fa-solid fa-star"></i>
                            <i class="fa-solid fa-star"></i>
                            <i class="fa-solid fa-star"></i>
                            <i class="fa-solid fa-star"></i>
                            <i class="fa-solid fa-star"></i>
                        </div>
                        <span class="text-gray-400 text-sm">5.0分</span>
                    </div>
                </div>
                <div class="text-right">
                    <p class="text-yellow-400 font-bold text-lg">¥180</p>
                    <p class="text-gray-400 text-sm">每小时</p>
                </div>
            </div>
        </div>

        <!-- Booking Details -->
        <div class="bg-gray-800 p-4 mx-4 rounded-xl mb-4">
            <h3 class="font-bold mb-4 flex items-center gap-2">
                <i class="fa-solid fa-calendar-check text-yellow-400"></i>
                预约详情
            </h3>
            <div class="space-y-3">
                <div class="flex justify-between">
                    <span class="text-gray-400">上课日期</span>
                    <span class="font-bold">2024年1月15日</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-400">上课时间</span>
                    <span class="font-bold">09:00 - 10:00</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-400">课程时长</span>
                    <span class="font-bold">1小时</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-400">上课地点</span>
                    <span class="font-bold">思明店</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-400">课程类型</span>
                    <span class="font-bold">一对一指导</span>
                </div>
            </div>
        </div>

        <!-- Contact Info -->
        <div class="bg-gray-800 p-4 mx-4 rounded-xl mb-4">
            <h3 class="font-bold mb-4 flex items-center gap-2">
                <i class="fa-solid fa-user text-yellow-400"></i>
                联系信息
            </h3>
            <div class="space-y-3">
                <div>
                    <label class="block text-gray-400 text-sm mb-1">姓名</label>
                    <input type="text" value="明亮的杰瑞" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg outline-none focus:ring-2 focus:ring-yellow-400">
                </div>
                <div>
                    <label class="block text-gray-400 text-sm mb-1">手机号</label>
                    <input type="tel" placeholder="请输入手机号" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg outline-none focus:ring-2 focus:ring-yellow-400">
                </div>
                <div>
                    <label class="block text-gray-400 text-sm mb-1">备注信息（可选）</label>
                    <textarea placeholder="请输入特殊需求或备注" class="w-full bg-gray-700 text-white px-3 py-2 rounded-lg outline-none focus:ring-2 focus:ring-yellow-400 h-20 resize-none"></textarea>
                </div>
            </div>
        </div>

        <!-- Course Options -->
        <div class="bg-gray-800 p-4 mx-4 rounded-xl mb-4">
            <h3 class="font-bold mb-4 flex items-center gap-2">
                <i class="fa-solid fa-cog text-yellow-400"></i>
                课程选项
            </h3>
            <div class="space-y-3">
                <div>
                    <label class="block text-gray-400 text-sm mb-2">学习目标</label>
                    <div class="grid grid-cols-2 gap-2">
                        <button class="goal-option bg-gray-700 text-white px-3 py-2 rounded-lg text-sm border-2 border-transparent" data-goal="basic">基础入门</button>
                        <button class="goal-option bg-yellow-400 text-black px-3 py-2 rounded-lg text-sm border-2 border-yellow-400 font-bold" data-goal="improve">技术提升</button>
                        <button class="goal-option bg-gray-700 text-white px-3 py-2 rounded-lg text-sm border-2 border-transparent" data-goal="competition">比赛准备</button>
                        <button class="goal-option bg-gray-700 text-white px-3 py-2 rounded-lg text-sm border-2 border-transparent" data-goal="correction">动作纠正</button>
                    </div>
                </div>
                <div>
                    <label class="block text-gray-400 text-sm mb-2">当前水平</label>
                    <div class="grid grid-cols-3 gap-2">
                        <button class="level-option bg-gray-700 text-white px-3 py-2 rounded-lg text-sm border-2 border-transparent" data-level="beginner">初学者</button>
                        <button class="level-option bg-yellow-400 text-black px-3 py-2 rounded-lg text-sm border-2 border-yellow-400 font-bold" data-level="intermediate">中级</button>
                        <button class="level-option bg-gray-700 text-white px-3 py-2 rounded-lg text-sm border-2 border-transparent" data-level="advanced">高级</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payment Method -->
        <div class="bg-gray-800 p-4 mx-4 rounded-xl mb-4">
            <h3 class="font-bold mb-4 flex items-center gap-2">
                <i class="fa-solid fa-credit-card text-yellow-400"></i>
                支付方式
            </h3>
            <div class="space-y-3">
                <label class="flex items-center gap-3 p-3 bg-gray-700 rounded-lg cursor-pointer">
                    <input type="radio" name="payment" value="wechat" class="text-yellow-400" checked>
                    <i class="fa-brands fa-weixin text-green-500 text-xl"></i>
                    <span class="flex-1">微信支付</span>
                    <i class="fa-solid fa-chevron-right text-gray-400"></i>
                </label>
                <label class="flex items-center gap-3 p-3 bg-gray-700 rounded-lg cursor-pointer">
                    <input type="radio" name="payment" value="alipay" class="text-yellow-400">
                    <i class="fa-brands fa-alipay text-blue-500 text-xl"></i>
                    <span class="flex-1">支付宝</span>
                    <i class="fa-solid fa-chevron-right text-gray-400"></i>
                </label>
                <label class="flex items-center gap-3 p-3 bg-gray-700 rounded-lg cursor-pointer">
                    <input type="radio" name="payment" value="balance" class="text-yellow-400">
                    <i class="fa-solid fa-wallet text-yellow-400 text-xl"></i>
                    <span class="flex-1">余额支付</span>
                    <span class="text-gray-400 text-sm">余额：¥0</span>
                </label>
            </div>
        </div>

        <!-- Price Summary -->
        <div class="bg-gray-800 p-4 mx-4 rounded-xl mb-4">
            <h3 class="font-bold mb-4 flex items-center gap-2">
                <i class="fa-solid fa-receipt text-yellow-400"></i>
                费用明细
            </h3>
            <div class="space-y-2">
                <div class="flex justify-between">
                    <span class="text-gray-400">课程费用</span>
                    <span>¥180</span>
                </div>
                <div class="flex justify-between">
                    <span class="text-gray-400">服务费</span>
                    <span>¥0</span>
                </div>
                <div class="flex justify-between text-green-400">
                    <span>新用户优惠</span>
                    <span>-¥20</span>
                </div>
                <div class="border-t border-gray-600 pt-2 mt-2">
                    <div class="flex justify-between text-lg font-bold">
                        <span>实付金额</span>
                        <span class="text-yellow-400">¥160</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Terms -->
        <div class="px-4 mb-4">
            <label class="flex items-start gap-2 text-sm text-gray-400">
                <input type="checkbox" class="mt-1" checked>
                <span>我已阅读并同意 <a href="#" class="text-yellow-400">《预约服务协议》</a> 和 <a href="#" class="text-yellow-400">《退款政策》</a></span>
            </label>
        </div>
    </div>

    <!-- Bottom Action Bar -->
    <div class="fixed bottom-0 left-0 right-0 bg-gray-900/90 backdrop-blur-md border-t border-gray-700 p-4">
        <div class="flex items-center justify-between mb-3">
            <span class="text-gray-400">实付金额</span>
            <span class="text-2xl font-bold text-yellow-400">¥160</span>
        </div>
        <button onclick="confirmBooking()" class="w-full bg-gradient-to-r from-yellow-400 to-orange-500 text-black py-3 rounded-lg font-bold glow-yellow">
            确认预约并支付
        </button>
    </div>

    <script>
        // 初始化页面
        document.addEventListener('DOMContentLoaded', function() {
            setupOptionSelectors();
        });

        function setupOptionSelectors() {
            // 学习目标选择
            document.querySelectorAll('.goal-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.goal-option').forEach(opt => {
                        opt.className = 'goal-option bg-gray-700 text-white px-3 py-2 rounded-lg text-sm border-2 border-transparent';
                    });
                    this.className = 'goal-option bg-yellow-400 text-black px-3 py-2 rounded-lg text-sm border-2 border-yellow-400 font-bold';
                });
            });

            // 水平选择
            document.querySelectorAll('.level-option').forEach(option => {
                option.addEventListener('click', function() {
                    document.querySelectorAll('.level-option').forEach(opt => {
                        opt.className = 'level-option bg-gray-700 text-white px-3 py-2 rounded-lg text-sm border-2 border-transparent';
                    });
                    this.className = 'level-option bg-yellow-400 text-black px-3 py-2 rounded-lg text-sm border-2 border-yellow-400 font-bold';
                });
            });
        }

        function confirmBooking() {
            // 验证必填信息
            const phone = document.querySelector('input[type="tel"]').value;
            if (!phone) {
                alert('请输入手机号');
                return;
            }

            // 验证手机号格式
            const phoneRegex = /^1[3-9]\d{9}$/;
            if (!phoneRegex.test(phone)) {
                alert('请输入正确的手机号');
                return;
            }

            // 确认预约
            if (confirm('确认预约并支付¥160？')) {
                // 模拟支付过程
                showPaymentProcess();
            }
        }

        function showPaymentProcess() {
            // 显示支付中状态
            const button = document.querySelector('button[onclick="confirmBooking()"]');
            button.innerHTML = '<i class="fa-solid fa-spinner fa-spin"></i> 支付中...';
            button.disabled = true;

            // 模拟支付延迟
            setTimeout(() => {
                // 跳转到支付成功页面
                window.location.href = 'booking_success.html';
            }, 2000);
        }
    </script>

</body>
</html>
