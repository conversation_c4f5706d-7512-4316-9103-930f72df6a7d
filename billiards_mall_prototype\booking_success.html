<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预约成功</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        body { background-color: #1A1A1A; color: white; font-family: sans-serif; }
        .success-animation {
            animation: successPulse 2s ease-in-out infinite;
        }
        @keyframes successPulse {
            0%, 100% { transform: scale(1); }
            50% { transform: scale(1.1); }
        }
        .glow-green { box-shadow: 0 0 20px rgba(34, 197, 94, 0.5); }
    </style>
</head>
<body class="pb-24">

    <!-- iOS Status Bar -->
    <div class="fixed top-0 left-0 right-0 h-11 bg-black/30 backdrop-blur-sm z-50 px-4 flex justify-between items-center text-sm font-semibold">
        <span>9:41</span>
        <div class="w-1/3 h-6 bg-black rounded-b-xl absolute top-0 left-1/2 -translate-x-1/2"></div>
        <div class="flex items-center gap-2"><i class="fa-solid fa-signal"></i><i class="fa-solid fa-wifi"></i><i class="fa-solid fa-battery-full"></i></div>
    </div>
    
    <!-- Header -->
    <div class="fixed top-11 left-0 right-0 bg-gray-900/90 backdrop-blur-md z-40 px-4 py-3 border-b border-gray-700">
        <div class="flex items-center justify-center">
            <h1 class="text-lg font-bold">预约成功</h1>
        </div>
    </div>

    <!-- Main Content -->
    <div class="pt-24 px-4">
        <!-- Success Icon -->
        <div class="text-center py-8">
            <div class="success-animation inline-block w-24 h-24 bg-green-500 rounded-full flex items-center justify-center mb-4 glow-green">
                <i class="fa-solid fa-check text-4xl text-white"></i>
            </div>
            <h2 class="text-2xl font-bold mb-2">预约成功！</h2>
            <p class="text-gray-400">您的课程已预约成功，请按时到场上课</p>
        </div>

        <!-- Booking Info Card -->
        <div class="bg-gray-800 rounded-2xl p-6 mb-6">
            <div class="flex items-center gap-4 mb-6">
                <img src="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?q=80&w=80&auto=format&fit=crop" class="w-16 h-16 rounded-full object-cover border-2 border-green-400" alt="Coach Avatar">
                <div class="flex-1">
                    <h3 class="text-lg font-bold">张教练</h3>
                    <div class="bg-gradient-to-r from-yellow-400 to-orange-500 text-black inline-block px-2 py-1 rounded-full text-xs font-bold mt-1">
                        国家一级教练
                    </div>
                </div>
                <div class="text-center">
                    <div class="bg-green-500 text-white px-3 py-1 rounded-full text-sm font-bold">
                        已预约
                    </div>
                </div>
            </div>

            <div class="space-y-4">
                <div class="flex items-center gap-3">
                    <i class="fa-solid fa-calendar-check text-green-400 w-5"></i>
                    <span class="text-gray-400">上课时间</span>
                    <span class="font-bold ml-auto">2024年1月15日 09:00-10:00</span>
                </div>
                <div class="flex items-center gap-3">
                    <i class="fa-solid fa-location-dot text-green-400 w-5"></i>
                    <span class="text-gray-400">上课地点</span>
                    <span class="font-bold ml-auto">思明店</span>
                </div>
                <div class="flex items-center gap-3">
                    <i class="fa-solid fa-clock text-green-400 w-5"></i>
                    <span class="text-gray-400">课程时长</span>
                    <span class="font-bold ml-auto">1小时</span>
                </div>
                <div class="flex items-center gap-3">
                    <i class="fa-solid fa-money-bill text-green-400 w-5"></i>
                    <span class="text-gray-400">支付金额</span>
                    <span class="font-bold ml-auto text-green-400">¥160</span>
                </div>
            </div>
        </div>

        <!-- Order Number -->
        <div class="bg-gray-800 rounded-xl p-4 mb-6">
            <div class="flex items-center justify-between">
                <span class="text-gray-400">订单号</span>
                <div class="flex items-center gap-2">
                    <span class="font-mono text-sm" id="orderNumber">BK20240115090001</span>
                    <button onclick="copyOrderNumber()" class="text-yellow-400 text-sm">
                        <i class="fa-solid fa-copy"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Important Notes -->
        <div class="bg-gradient-to-r from-yellow-400/20 to-orange-500/20 border border-yellow-400/30 rounded-xl p-4 mb-6">
            <h3 class="font-bold mb-3 flex items-center gap-2">
                <i class="fa-solid fa-exclamation-triangle text-yellow-400"></i>
                重要提醒
            </h3>
            <div class="space-y-2 text-sm">
                <p>• 请提前10分钟到达球馆，避免迟到影响上课</p>
                <p>• 建议穿着运动服装，携带运动鞋</p>
                <p>• 如需取消或改期，请提前24小时联系客服</p>
                <p>• 教练联系方式将在上课前1小时发送短信通知</p>
            </div>
        </div>

        <!-- Contact Info -->
        <div class="bg-gray-800 rounded-xl p-4 mb-6">
            <h3 class="font-bold mb-3">联系方式</h3>
            <div class="space-y-3">
                <div class="flex items-center justify-between">
                    <span class="text-gray-400">球馆电话</span>
                    <a href="tel:0592-1234567" class="text-yellow-400 font-bold">0592-1234567</a>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-gray-400">客服热线</span>
                    <a href="tel:************" class="text-yellow-400 font-bold">************</a>
                </div>
                <div class="flex items-center justify-between">
                    <span class="text-gray-400">球馆地址</span>
                    <button onclick="openMap()" class="text-yellow-400 font-bold">
                        厦门市思明区XX路XX号 <i class="fa-solid fa-external-link text-xs"></i>
                    </button>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="grid grid-cols-2 gap-4 mb-6">
            <button onclick="addToCalendar()" class="bg-gray-800 p-4 rounded-xl text-center">
                <i class="fa-solid fa-calendar-plus text-2xl text-blue-400 mb-2"></i>
                <p class="text-sm font-bold">添加到日历</p>
            </button>
            <button onclick="shareBooking()" class="bg-gray-800 p-4 rounded-xl text-center">
                <i class="fa-solid fa-share text-2xl text-green-400 mb-2"></i>
                <p class="text-sm font-bold">分享预约</p>
            </button>
        </div>

        <!-- Action Buttons -->
        <div class="space-y-3">
            <button onclick="viewMyBookings()" class="w-full bg-yellow-400 text-black py-3 rounded-lg font-bold">
                查看我的预约
            </button>
            <button onclick="bookAgain()" class="w-full bg-gray-700 text-white py-3 rounded-lg font-bold">
                再次预约
            </button>
            <button onclick="goHome()" class="w-full bg-transparent border border-gray-600 text-gray-300 py-3 rounded-lg font-bold">
                返回首页
            </button>
        </div>
    </div>

    <!-- Bottom Tab Bar -->
    <div class="fixed bottom-0 left-0 right-0 h-20 bg-gray-900/80 backdrop-blur-md flex justify-around items-center text-gray-400 border-t border-gray-700">
        <a href="home_rich.html" class="flex flex-col items-center gap-1"><i class="fa-solid fa-house text-xl"></i><span class="text-xs">首页</span></a>
        <a href="mall_list_enhanced.html" class="flex flex-col items-center gap-1"><i class="fa-solid fa-store text-xl"></i><span class="text-xs">商城</span></a>
        <a href="#" class="w-16 h-16 -mt-8 bg-yellow-400 rounded-full flex items-center justify-center text-black shadow-lg"><i class="fa-solid fa-qrcode text-3xl"></i></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-file-invoice text-xl"></i><span class="text-xs">订单</span></a>
        <a href="#" class="flex flex-col items-center gap-1"><i class="fa-solid fa-user text-xl"></i><span class="text-xs">我的</span></a>
    </div>

    <script>
        // 页面加载时播放成功音效（如果需要）
        document.addEventListener('DOMContentLoaded', function() {
            // 可以添加成功音效
            // playSuccessSound();
            
            // 自动发送确认短信（模拟）
            setTimeout(() => {
                showNotification('预约确认短信已发送到您的手机');
            }, 2000);
        });

        function copyOrderNumber() {
            const orderNumber = document.getElementById('orderNumber').textContent;
            navigator.clipboard.writeText(orderNumber).then(() => {
                showNotification('订单号已复制到剪贴板');
            });
        }

        function addToCalendar() {
            // 创建日历事件
            const event = {
                title: '台球课程 - 张教练',
                start: '2024-01-15T09:00:00',
                end: '2024-01-15T10:00:00',
                description: '台球一对一指导课程',
                location: '厦门市思明区XX路XX号'
            };
            
            // 生成日历链接
            const calendarUrl = `https://calendar.google.com/calendar/render?action=TEMPLATE&text=${encodeURIComponent(event.title)}&dates=${event.start.replace(/[-:]/g, '')}/${event.end.replace(/[-:]/g, '')}&details=${encodeURIComponent(event.description)}&location=${encodeURIComponent(event.location)}`;
            
            window.open(calendarUrl, '_blank');
        }

        function shareBooking() {
            if (navigator.share) {
                navigator.share({
                    title: '我预约了台球课程',
                    text: '我在小铁台球预约了张教练的课程，一起来学台球吧！',
                    url: window.location.href
                });
            } else {
                // 降级处理
                const shareText = '我在小铁台球预约了张教练的课程，一起来学台球吧！';
                navigator.clipboard.writeText(shareText).then(() => {
                    showNotification('分享内容已复制到剪贴板');
                });
            }
        }

        function openMap() {
            // 打开地图应用
            const address = '厦门市思明区XX路XX号';
            const mapUrl = `https://maps.google.com/maps?q=${encodeURIComponent(address)}`;
            window.open(mapUrl, '_blank');
        }

        function viewMyBookings() {
            window.location.href = 'my_bookings.html';
        }

        function bookAgain() {
            window.location.href = 'coach_list.html';
        }

        function goHome() {
            window.location.href = 'home_rich.html';
        }

        function showNotification(message) {
            // 创建通知元素
            const notification = document.createElement('div');
            notification.className = 'fixed top-20 left-1/2 transform -translate-x-1/2 bg-green-500 text-white px-4 py-2 rounded-lg z-50';
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // 3秒后移除通知
            setTimeout(() => {
                notification.remove();
            }, 3000);
        }
    </script>

</body>
</html>
